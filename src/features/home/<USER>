// Home Feature Configuration
// This file manages all version configurations for the home feature

export const homeFeatureConfig = {
  // Page-level configurations
  pages: {
    homePage: 'v1',           // Main home page (currently in app/page.tsx)
  },

  // Component-level configurations
  components: {
    hero: 'v5',               // Hero section (currently active)
    breakingNews: 'v1',       // Breaking news component
    upcomingFixtures: 'v2',   // Upcoming fixtures component
    engagementFeatures: 'v2', // Engagement features component
    footerContent: 'v2',      // Footer content component
    featuredLeagues: 'v1',    // Featured leagues (no versioning yet)
    quickNav: 'v1',           // Quick navigation (no versioning yet)
  }
} as const;

export type HomeFeatureConfig = typeof homeFeatureConfig;

// Environment-based configurations
export const HOME_FEATURE_VERSIONS = {
  development: {
    ...homeFeatureConfig,
    components: {
      ...homeFeatureConfig.components,
      hero: 'v5',               // Latest for development
      engagementFeatures: 'v2', // Latest for development
    }
  },
  production: {
    ...homeFeatureConfig,
    components: {
      ...homeFeatureConfig.components,
      hero: 'v5',               // Stable for production
      engagementFeatures: 'v2', // Stable for production
    }
  }
} as const;

// Get current configuration based on environment
export const getCurrentHomeConfig = () => {
  const env = process.env.NODE_ENV as keyof typeof HOME_FEATURE_VERSIONS;
  return HOME_FEATURE_VERSIONS[env] || HOME_FEATURE_VERSIONS.production;
};

// Version metadata for documentation
export const HOME_VERSION_INFO = {
  hero: {
    v1: { name: 'Classic/Traditional', status: 'stable', releaseDate: '2024-01-01' },
    v2: { name: 'Modern Sports Dashboard', status: 'stable', releaseDate: '2024-01-05' },
    v3: { name: '3D Immersive Sports Arena', status: 'stable', releaseDate: '2024-01-10' },
    v4: { name: 'Sport Style with Live/Upcoming Matches', status: 'stable', releaseDate: '2024-01-12' },
    v5: { name: 'Dynamic Sports Command Center', status: 'stable', releaseDate: '2024-01-15' },
  },
  breakingNews: {
    v1: { name: 'Breaking News V1', status: 'stable', releaseDate: '2024-01-20' },
  },
  upcomingFixtures: {
    v1: { name: 'Clean Timeline Design', status: 'stable', releaseDate: '2024-01-18' },
    v2: { name: 'Modern Schedule Viewer', status: 'stable', releaseDate: '2024-01-22' },
  },
  engagementFeatures: {
    v1: { name: 'Initial Implementation', status: 'stable', releaseDate: '2024-01-15' },
    v2: { name: 'Enhanced UI/UX', status: 'stable', releaseDate: '2024-01-20' },
  },
  footerContent: {
    v1: { name: 'Initial Implementation', status: 'stable', releaseDate: '2024-01-15' },
    v2: { name: 'Minimalist Design', status: 'stable', releaseDate: '2024-01-20' },
  }
} as const;

// Helper function to get component version
export const getComponentVersion = (componentName: keyof typeof homeFeatureConfig.components) => {
  const config = getCurrentHomeConfig();
  return config.components[componentName];
};

// Helper function to get version info
export const getVersionInfo = (componentName: keyof typeof HOME_VERSION_INFO, version: string) => {
  return HOME_VERSION_INFO[componentName]?.[version as keyof typeof HOME_VERSION_INFO[typeof componentName]];
};
