// Advanced 3D Magnetic Field Effects System
import { useState, useEffect, useRef, useMemo, useCallback } from 'react';

export interface MagneticField {
  id: string;
  x: number;
  y: number;
  z: number;
  radius: number;
  strength: number;
  type: 'attract' | 'repel';
  color: string;
  opacity: number;
  pulse: boolean;
  active: boolean;
  decayRate: number; // How quickly field strength diminishes over time
  createdAt: number;
  lifespan: number; // How long the field exists in ms
}

export interface MagneticFieldInteraction {
  source: string; // ID of source particle
  target: string; // ID of target particle or field
  strength: number;
  type: 'particle-field' | 'field-field';
  color: string;
  visible: boolean;
}

interface MagneticFieldSystemConfig {
  maxFields: number;
  enableVisualization: boolean;
  fieldLifespan: number;
  defaultStrength: number;
  interactionThreshold: number;
  enableFieldInteractions: boolean;
  performanceMode: boolean;
  enableMobileOptimization?: boolean;
  mobileOptimization?: boolean; // Legacy support
  enableAccessibilityMode?: boolean;
  accessibilityMode?: boolean; // Legacy support
  enableAudioSystem?: boolean;
  enableParticleSystem?: boolean;
  audioSystem?: any; // Legacy support
  particleSystem?: any; // Legacy support
}

interface PerformanceMetrics {
  fieldCount: number;
  interactionCount: number;
  frameRate: number;
  framesPerSecond: number;
  memoryUsage: number;
  lastUpdateTime: number;
}

interface DebugMetrics {
  isMobile: boolean;
  isAccessibilityMode: boolean;
  performanceMode: boolean;
  fieldCreationRate: number;
  averageFieldLifespan: number;
}

export const useMagneticFieldSystem = (config: MagneticFieldSystemConfig) => {
  const [magneticFields, setMagneticFields] = useState<MagneticField[]>([]);
  const [fieldInteractions, setFieldInteractions] = useState<MagneticFieldInteraction[]>([]);
  const [isActive, setIsActive] = useState(true);
  const animationFrameRef = useRef<number>();
  const lastUpdateRef = useRef<number>(Date.now());
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>({
    fieldCount: 0,
    interactionCount: 0,
    frameRate: 60,
    framesPerSecond: 60,
    memoryUsage: 0,
    lastUpdateTime: Date.now()
  });

  // Detect mobile device
  const isMobile = useMemo(() => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth < 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }, []);

  // Debug metrics
  const debugMetrics = useMemo((): DebugMetrics => ({
    isMobile,
    isAccessibilityMode: config.enableAccessibilityMode || config.accessibilityMode || false,
    performanceMode: config.performanceMode,
    fieldCreationRate: magneticFields.length / Math.max(1, (Date.now() - lastUpdateRef.current) / 1000),
    averageFieldLifespan: magneticFields.length > 0
      ? magneticFields.reduce((sum, field) => sum + (Date.now() - field.createdAt), 0) / magneticFields.length
      : 0
  }), [isMobile, config.enableAccessibilityMode, config.accessibilityMode, config.performanceMode, magneticFields]);

  // Mock audio system integration
  const audioSystem = useMemo(() => ({
    playFieldCreation: (type: string, strength: number) => {
      if (config.enableAudioSystem) {
        // Mock audio implementation
        console.log(`Playing field creation sound: ${type}, strength: ${strength}`);
      }
    }
  }), [config.enableAudioSystem]);

  // Mock particle system integration
  const particleSystem = useMemo(() => ({
    createFieldParticles: (field: { x: number; y: number; radius: number; type: string }) => {
      if (config.enableParticleSystem) {
        // Mock particle implementation
        console.log(`Creating particles for field at (${field.x}, ${field.y})`);
      }
    }
  }), [config.enableParticleSystem]);

  // Create a new magnetic field
  const createMagneticField = useCallback((
    x: number,
    y: number,
    options: Partial<Omit<MagneticField, 'id' | 'createdAt'>> = {}
  ) => {
    const now = Date.now();
    const id = `magnetic-field-${now}-${Math.random().toString(36).substr(2, 9)}`;

    // Apply mobile optimizations
    let radius = options.radius || 50 + Math.random() * 100;
    let strength = options.strength || config.defaultStrength * (0.5 + Math.random());
    let pulse = options.pulse !== undefined ? options.pulse : Math.random() > 0.3;

    if ((config.enableMobileOptimization || config.mobileOptimization) && isMobile) {
      // For mobile, always apply optimizations
      radius = Math.min(radius, 80); // Cap radius for mobile
      strength = Math.min(strength, 80); // Cap strength for mobile
      pulse = false; // Disable pulse for mobile performance
    }

    // Apply accessibility mode settings
    if (config.enableAccessibilityMode || config.accessibilityMode) {
      pulse = false; // Disable pulse in accessibility mode
    }

    const newField: MagneticField = {
      id,
      x,
      y,
      z: options.z || 0,
      radius,
      strength,
      type: options.type || (Math.random() > 0.5 ? 'attract' : 'repel'),
      color: options.color || (options.type === 'attract' ? '#3b82f6' : '#ef4444'),
      opacity: options.opacity || 0.3 + Math.random() * 0.3,
      pulse,
      active: options.active !== undefined ? options.active : true,
      decayRate: options.decayRate || 0.01 + Math.random() * 0.03,
      createdAt: now,
      lifespan: options.lifespan || config.fieldLifespan * (0.7 + Math.random() * 0.6)
    };

    // Call audio system if enabled
    if (config.enableAudioSystem || config.audioSystem) {
      if (config.audioSystem) {
        config.audioSystem.playFieldCreation(newField.type, newField.strength);
      } else {
        audioSystem.playFieldCreation(newField.type, newField.strength);
      }
    }

    // Call particle system if enabled
    if (config.enableParticleSystem || config.particleSystem) {
      if (config.particleSystem) {
        config.particleSystem.createFieldParticles({
          x: newField.x,
          y: newField.y,
          radius: newField.radius,
          type: newField.type
        });
      } else {
        particleSystem.createFieldParticles({
          x: newField.x,
          y: newField.y,
          radius: newField.radius,
          type: newField.type
        });
      }
    }

    setMagneticFields(current => {
      const updated = [...current, newField];

      // Remove oldest fields if we exceed max fields
      if (updated.length > config.maxFields) {
        return updated.slice(updated.length - config.maxFields);
      }

      return updated;
    });

    return id;
  }, [config.defaultStrength, config.fieldLifespan, config.maxFields, config.enableMobileOptimization, config.mobileOptimization, config.enableAccessibilityMode, config.accessibilityMode, config.enableAudioSystem, config.enableParticleSystem, config.audioSystem, config.particleSystem, isMobile, audioSystem, particleSystem]);

  // Create a goal celebration field burst
  const createGoalCelebrationFields = useCallback((x: number, y: number, intensity: number = 1) => {
    const fieldCount = Math.floor(4 + intensity * 4);
    const fieldIds: string[] = [];

    // Create central explosion field
    fieldIds.push(createMagneticField(x, y, {
      radius: 150 * intensity,
      strength: config.defaultStrength * 3 * intensity,
      type: 'repel',
      color: '#10b981', // Green
      opacity: 0.6,
      pulse: true,
      lifespan: config.fieldLifespan * 0.7
    }));

    // Create surrounding fields
    for (let i = 0; i < fieldCount; i++) {
      const angle = (Math.PI * 2) * (i / fieldCount);
      const distance = 100 + Math.random() * 150;

      fieldIds.push(createMagneticField(
        x + Math.cos(angle) * distance,
        y + Math.sin(angle) * distance,
        {
          radius: 50 + Math.random() * 70,
          strength: config.defaultStrength * (1 + Math.random()),
          type: i % 2 === 0 ? 'attract' : 'repel',
          color: i % 3 === 0 ? '#f59e0b' : i % 3 === 1 ? '#3b82f6' : '#8b5cf6',
          opacity: 0.3 + Math.random() * 0.4,
          pulse: true,
          lifespan: config.fieldLifespan * (0.5 + Math.random() * 0.5)
        }
      ));
    }

    return fieldIds;
  }, [createMagneticField, config.defaultStrength, config.fieldLifespan]);

  // Create energy surge fields along a path
  const createEnergySurgeFields = useCallback((
    path: { x: number; y: number }[],
    options: Partial<Omit<MagneticField, 'id' | 'createdAt' | 'x' | 'y'>> = {}
  ) => {
    const fieldIds: string[] = [];

    // Create magnetic fields along the path with staggered timing
    path.forEach((point, index) => {
      setTimeout(() => {
        const fieldId = createMagneticField(point.x, point.y, {
          radius: options.radius || 30 + Math.random() * 20,
          strength: options.strength || 50 + Math.random() * 30,
          type: options.type || 'attract',
          color: options.color || '#3b82f6',
          opacity: options.opacity || 0.4 + Math.random() * 0.3,
          pulse: options.pulse !== undefined ? options.pulse : true,
          decayRate: options.decayRate || 0.03 + Math.random() * 0.02,
          lifespan: options.lifespan || config.fieldLifespan * 0.6,
          z: options.z || index * 2
        });

        fieldIds.push(fieldId);
      }, index * 100); // Stagger creation for more natural effect
    });

    return fieldIds;
  }, [createMagneticField, config.fieldLifespan]);

  // Calculate field interactions
  const calculateFieldInteractions = useCallback(() => {
    if (!config.enableFieldInteractions || magneticFields.length <= 1) {
      setFieldInteractions([]);
      return;
    }

    const interactions: MagneticFieldInteraction[] = [];

    // Check for interactions between fields
    for (let i = 0; i < magneticFields.length; i++) {
      for (let j = i + 1; j < magneticFields.length; j++) {
        const field1 = magneticFields[i];
        const field2 = magneticFields[j];

        // Calculate distance between fields
        const dx = field2.x - field1.x;
        const dy = field2.y - field1.y;
        const dz = field2.z - field1.z;
        const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

        // Check if fields are close enough to interact
        const interactionRange = field1.radius + field2.radius;
        if (distance < interactionRange) {
          const strengthFactor = Math.min(1, (interactionRange - distance) / interactionRange);
          const interactionStrength = field1.strength * field2.strength * strengthFactor;

          // Only show significant interactions
          if (interactionStrength > config.interactionThreshold) {
            // Determine interaction color based on field types
            let color = '#ffffff';
            if (field1.type === 'attract' && field2.type === 'attract') {
              color = '#3b82f6'; // Blue
            } else if (field1.type === 'repel' && field2.type === 'repel') {
              color = '#ef4444'; // Red
            } else {
              color = '#8b5cf6'; // Purple for mixed types
            }

            interactions.push({
              source: field1.id,
              target: field2.id,
              strength: interactionStrength,
              type: 'field-field',
              color,
              visible: config.enableVisualization
            });
          }
        }
      }
    }

    setFieldInteractions(interactions);
  }, [config.enableFieldInteractions, config.enableVisualization, config.interactionThreshold, magneticFields]);

  // Update magnetic fields (fade out, remove expired)
  useEffect(() => {
    if (!isActive) return;

    const updateFields = () => {
      const now = Date.now();
      const deltaTime = now - lastUpdateRef.current;
      lastUpdateRef.current = now;

      setMagneticFields(currentFields => {
        // Update each field
        const updatedFields = currentFields
          .map(field => {
            // Calculate age and remaining lifespan
            const age = now - field.createdAt;
            const remainingLifespan = field.lifespan - age;

            if (remainingLifespan <= 0) {
              return null; // Field expired
            }

            // Calculate decay based on age
            const lifeProgress = age / field.lifespan;
            const decayFactor = 1 - lifeProgress;

            return {
              ...field,
              strength: field.strength * (1 - field.decayRate * (deltaTime / 16)),
              opacity: field.opacity * decayFactor
            };
          })
          .filter(Boolean) as MagneticField[];

        return updatedFields;
      });

      // Calculate field interactions
      calculateFieldInteractions();

      // Continue animation loop
      animationFrameRef.current = requestAnimationFrame(updateFields);
    };

    // Start animation loop
    animationFrameRef.current = requestAnimationFrame(updateFields);

    // Cleanup
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isActive, calculateFieldInteractions]);

  // Clear all fields
  const clearAllFields = useCallback(() => {
    setMagneticFields([]);
    setFieldInteractions([]);
  }, []);

  // Reset the system
  const resetSystem = useCallback(() => {
    clearAllFields();
    setIsActive(true);
  }, [clearAllFields]);

  // Pause/resume system
  const toggleActive = useCallback(() => {
    setIsActive(prev => !prev);
  }, []);

  // Get performance metrics
  const getPerformanceMetrics = useCallback((): PerformanceMetrics => {
    return {
      ...performanceMetrics,
      fieldCount: magneticFields.length,
      interactionCount: fieldInteractions.length,
      framesPerSecond: performanceMetrics.frameRate,
      lastUpdateTime: Date.now()
    };
  }, [performanceMetrics, magneticFields.length, fieldInteractions.length]);

  // Generate visualization data for rendering
  const visualizationData = useMemo(() => {
    if (!config.enableVisualization) return { fields: [], interactions: [] };

    // Process fields for visualization
    const fieldData = magneticFields.map(field => {
      const pulseAmplitude = field.pulse ? Math.sin(Date.now() * 0.003) * 0.2 + 0.8 : 1;

      return {
        ...field,
        visualRadius: field.radius * pulseAmplitude,
        visualOpacity: field.opacity * pulseAmplitude,
        gradient: field.type === 'attract'
          ? `radial-gradient(circle, ${field.color}80 0%, ${field.color}00 100%)`
          : `radial-gradient(circle, ${field.color}80 30%, ${field.color}00 100%)`
      };
    });

    // Process interactions for visualization
    const interactionData = fieldInteractions.map(interaction => {
      const sourceField = magneticFields.find(f => f.id === interaction.source);
      const targetField = magneticFields.find(f => f.id === interaction.target);

      if (!sourceField || !targetField) return null;

      const dx = targetField.x - sourceField.x;
      const dy = targetField.y - sourceField.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      const angle = Math.atan2(dy, dx) * (180 / Math.PI);

      return {
        ...interaction,
        x1: sourceField.x,
        y1: sourceField.y,
        x2: targetField.x,
        y2: targetField.y,
        length: distance,
        angle,
        opacity: interaction.strength * 0.2
      };
    }).filter(Boolean);

    return {
      fields: fieldData,
      interactions: interactionData as Array<{
        x1: number;
        y1: number;
        x2: number;
        y2: number;
        length: number;
        angle: number;
        opacity: number;
        source: string;
        target: string;
        strength: number;
        type: 'particle-field' | 'field-field';
        color: string;
        visible: boolean;
      }>
    };
  }, [config.enableVisualization, magneticFields, fieldInteractions]);

  return {
    magneticFields,
    fieldInteractions,
    createMagneticField,
    createGoalCelebrationFields,
    createEnergySurgeFields,
    clearAllFields,
    resetSystem,
    toggleActive,
    isActive,
    visualizationData,
    debugMetrics,
    getPerformanceMetrics
  };
};
