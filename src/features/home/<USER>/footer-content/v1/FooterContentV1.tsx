'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { FooterContentProps, FooterContentData, PopularTeam, PopularPlayer, AppDownloadLink } from './types';

export const FooterContentV1: React.FC<FooterContentProps> = ({
  className = '',
  showPopularTeams = true,
  showPopularPlayers = true,
  showAppDownload = true,
  showSocialLinks = true,
  showCompanyInfo = true,
  maxTeams = 6,
  maxPlayers = 6,
  theme = 'dark'
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState<FooterContentData | null>(null);

  // Mock data - replace with real API data
  const mockData: FooterContentData = {
    popularTeams: [
      {
        id: '1',
        name: 'Manchester United',
        slug: 'manchester-united',
        logo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/public/images/teams/33.png`,
        league: 'Premier League',
        leagueId: '39',
        followers: 75000000,
        isVerified: true,
        country: 'England'
      },
      {
        id: '2',
        name: 'Real Madrid',
        slug: 'real-madrid',
        logo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/public/images/teams/541.png`,
        league: 'La Liga',
        leagueId: '140',
        followers: *********,
        isVerified: true,
        country: 'Spain'
      },
      {
        id: '3',
        name: 'Barcelona',
        slug: 'barcelona',
        logo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/public/images/teams/529.png`,
        league: 'La Liga',
        leagueId: '140',
        followers: 103000000,
        isVerified: true,
        country: 'Spain'
      },
      {
        id: '4',
        name: 'Liverpool',
        slug: 'liverpool',
        logo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/public/images/teams/40.png`,
        league: 'Premier League',
        leagueId: '39',
        followers: 45000000,
        isVerified: true,
        country: 'England'
      },
      {
        id: '5',
        name: 'Manchester City',
        slug: 'manchester-city',
        logo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/public/images/teams/50.png`,
        league: 'Premier League',
        leagueId: '39',
        followers: 38000000,
        isVerified: true,
        country: 'England'
      },
      {
        id: '6',
        name: 'PSG',
        slug: 'psg',
        logo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/public/images/teams/85.png`,
        league: 'Ligue 1',
        leagueId: '61',
        followers: 42000000,
        isVerified: true,
        country: 'France'
      }
    ],
    popularPlayers: [
      {
        id: '1',
        name: 'Lionel Messi',
        slug: 'lionel-messi',
        photo: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=100&h=100&fit=crop&crop=face',
        position: 'Forward',
        team: 'Inter Miami',
        teamId: '1',
        teamLogo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/public/images/teams/1.png`,
        nationality: 'Argentina',
        age: 36,
        marketValue: '€25M',
        isTopScorer: true
      },
      {
        id: '2',
        name: 'Cristiano Ronaldo',
        slug: 'cristiano-ronaldo',
        photo: 'https://images.unsplash.com/photo-1560272564-c83b66b1ad12?w=100&h=100&fit=crop&crop=face',
        position: 'Forward',
        team: 'Al Nassr',
        teamId: '2',
        teamLogo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/public/images/teams/2.png`,
        nationality: 'Portugal',
        age: 39,
        marketValue: '€15M',
        isTopScorer: true
      },
      {
        id: '3',
        name: 'Kylian Mbappé',
        slug: 'kylian-mbappe',
        photo: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=100&h=100&fit=crop&crop=face',
        position: 'Forward',
        team: 'Real Madrid',
        teamId: '541',
        teamLogo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/public/images/teams/541.png`,
        nationality: 'France',
        age: 25,
        marketValue: '€180M'
      },
      {
        id: '4',
        name: 'Erling Haaland',
        slug: 'erling-haaland',
        photo: 'https://images.unsplash.com/photo-1508098682722-e99c43a406b2?w=100&h=100&fit=crop&crop=face',
        position: 'Forward',
        team: 'Manchester City',
        teamId: '50',
        teamLogo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/public/images/teams/50.png`,
        nationality: 'Norway',
        age: 24,
        marketValue: '€170M',
        isTopScorer: true
      },
      {
        id: '5',
        name: 'Vinicius Jr.',
        slug: 'vinicius-jr',
        photo: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=100&h=100&fit=crop&crop=face',
        position: 'Winger',
        team: 'Real Madrid',
        teamId: '541',
        teamLogo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/public/images/teams/541.png`,
        nationality: 'Brazil',
        age: 24,
        marketValue: '€120M'
      },
      {
        id: '6',
        name: 'Jude Bellingham',
        slug: 'jude-bellingham',
        photo: 'https://images.unsplash.com/photo-1560272564-c83b66b1ad12?w=100&h=100&fit=crop&crop=face',
        position: 'Midfielder',
        team: 'Real Madrid',
        teamId: '541',
        teamLogo: `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/public/images/teams/541.png`,
        nationality: 'England',
        age: 21,
        marketValue: '€150M'
      }
    ],
    appDownloads: [
      {
        platform: 'ios',
        url: 'https://apps.apple.com/app/sports-game',
        icon: '🍎',
        label: 'Download for iOS',
        version: '2.1.0',
        size: '45.2 MB',
        rating: 4.8,
        downloads: '1M+'
      },
      {
        platform: 'android',
        url: 'https://play.google.com/store/apps/details?id=com.sportsgame',
        icon: '🤖',
        label: 'Get it on Google Play',
        version: '2.1.0',
        size: '38.7 MB',
        rating: 4.7,
        downloads: '5M+'
      },
      {
        platform: 'web',
        url: 'https://app.sportsgame.com',
        icon: '🌐',
        label: 'Open Web App',
        version: '2.1.0'
      }
    ],
    socialLinks: [
      {
        platform: 'facebook',
        url: 'https://facebook.com/sportsgame',
        icon: '📘',
        label: 'Facebook',
        followers: '2.5M'
      },
      {
        platform: 'twitter',
        url: 'https://twitter.com/sportsgame',
        icon: '🐦',
        label: 'Twitter',
        followers: '1.8M'
      },
      {
        platform: 'instagram',
        url: 'https://instagram.com/sportsgame',
        icon: '📷',
        label: 'Instagram',
        followers: '3.2M'
      },
      {
        platform: 'youtube',
        url: 'https://youtube.com/sportsgame',
        icon: '📺',
        label: 'YouTube',
        followers: '890K'
      }
    ],
    footerSections: [
      {
        title: 'Quick Links',
        links: [
          { label: 'Live Scores', href: '/live-scores' },
          { label: 'Fixtures', href: '/fixtures' },
          { label: 'Results', href: '/results' },
          { label: 'Standings', href: '/standings' }
        ]
      },
      {
        title: 'Leagues',
        links: [
          { label: 'Premier League', href: '/leagues/premier-league' },
          { label: 'La Liga', href: '/leagues/la-liga' },
          { label: 'Champions League', href: '/leagues/champions-league' },
          { label: 'World Cup', href: '/leagues/world-cup' }
        ]
      },
      {
        title: 'Support',
        links: [
          { label: 'Help Center', href: '/help' },
          { label: 'Contact Us', href: '/contact' },
          { label: 'Privacy Policy', href: '/privacy' },
          { label: 'Terms of Service', href: '/terms' }
        ]
      }
    ],
    companyInfo: {
      name: 'Sports Game',
      description: 'Your ultimate destination for live football scores, news, and analytics.',
      logo: '/logo.png',
      copyright: '© 2024 Sports Game. All rights reserved.',
      establishedYear: 2020
    },
    contactInfo: {
      email: '<EMAIL>',
      phone: '+****************',
      address: '123 Sports Avenue, Game City, GC 12345',
      supportHours: '24/7 Support Available'
    }
  };

  useEffect(() => {
    // Simulate API call
    const loadData = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 800));
      setData(mockData);
      setIsLoading(false);
    };

    loadData();
  }, []);

  // Format followers count
  const formatFollowers = (count: number) => {
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
    if (count >= 1000) return `${(count / 1000).toFixed(1)}K`;
    return count.toString();
  };

  // Get theme classes
  const getThemeClasses = () => {
    return theme === 'dark'
      ? 'bg-gray-900 text-white border-gray-700'
      : 'bg-gray-50 text-gray-900 border-gray-200';
  };

  const getCardThemeClasses = () => {
    return theme === 'dark'
      ? 'bg-gray-800 hover:bg-gray-700 border-gray-700'
      : 'bg-white hover:bg-gray-50 border-gray-200';
  };

  if (isLoading || !data) {
    return (
      <footer className={`${getThemeClasses()} py-16 border-t ${className}`}>
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="space-y-4">
                <div className="h-6 bg-gray-300 rounded animate-pulse"></div>
                <div className="space-y-2">
                  {[1, 2, 3, 4].map((j) => (
                    <div key={j} className="h-4 bg-gray-200 rounded animate-pulse"></div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </footer>
    );
  }

  return (
    <footer className={`${getThemeClasses()} py-16 border-t ${className}`}>
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
          {/* Popular Teams Section */}
          {showPopularTeams && (
            <div className="lg:col-span-1">
              <h3 className="text-xl font-bold mb-6 flex items-center space-x-2">
                <span className="text-2xl">⚽</span>
                <span>Popular Teams</span>
              </h3>
              <div className="space-y-3">
                {data.popularTeams.slice(0, maxTeams).map((team) => (
                  <Link
                    key={team.id}
                    href={`/teams/${team.slug}`}
                    className={`flex items-center space-x-3 p-3 rounded-lg border transition-all duration-300 hover:scale-105 ${getCardThemeClasses()}`}
                  >
                    <img
                      src={team.logo}
                      alt={team.name}
                      className="w-8 h-8 rounded"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `data:image/svg+xml,${encodeURIComponent(`
                          <svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
                            <rect width="100%" height="100%" fill="#3B82F6"/>
                            <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-size="12" fill="white">⚽</text>
                          </svg>
                        `)}`;
                      }}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-1">
                        <span className="font-medium text-sm truncate">{team.name}</span>
                        {team.isVerified && (
                          <span className="text-blue-500 text-xs">✓</span>
                        )}
                      </div>
                      <div className="flex items-center justify-between text-xs opacity-75">
                        <span>{team.league}</span>
                        <span>{formatFollowers(team.followers)} fans</span>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
              <Link
                href="/teams"
                className="inline-flex items-center space-x-1 text-blue-500 hover:text-blue-600 text-sm font-medium mt-4 transition-colors"
              >
                <span>View All Teams</span>
                <span>→</span>
              </Link>
            </div>
          )}

          {/* Popular Players Section */}
          {showPopularPlayers && (
            <div className="lg:col-span-1">
              <h3 className="text-xl font-bold mb-6 flex items-center space-x-2">
                <span className="text-2xl">⭐</span>
                <span>Popular Players</span>
              </h3>
              <div className="space-y-3">
                {data.popularPlayers.slice(0, maxPlayers).map((player) => (
                  <Link
                    key={player.id}
                    href={`/players/${player.slug}`}
                    className={`flex items-center space-x-3 p-3 rounded-lg border transition-all duration-300 hover:scale-105 ${getCardThemeClasses()}`}
                  >
                    <img
                      src={player.photo}
                      alt={player.name}
                      className="w-8 h-8 rounded-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `data:image/svg+xml,${encodeURIComponent(`
                          <svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="16" cy="16" r="16" fill="#8B5CF6"/>
                            <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-size="12" fill="white">👤</text>
                          </svg>
                        `)}`;
                      }}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-1">
                        <span className="font-medium text-sm truncate">{player.name}</span>
                        {player.isTopScorer && (
                          <span className="text-yellow-500 text-xs">🏆</span>
                        )}
                      </div>
                      <div className="flex items-center justify-between text-xs opacity-75">
                        <span>{player.position}</span>
                        <span>{player.team}</span>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
              <Link
                href="/players"
                className="inline-flex items-center space-x-1 text-blue-500 hover:text-blue-600 text-sm font-medium mt-4 transition-colors"
              >
                <span>View All Players</span>
                <span>→</span>
              </Link>
            </div>
          )}

          {/* App Download Section */}
          {showAppDownload && (
            <div className="lg:col-span-1">
              <h3 className="text-xl font-bold mb-6 flex items-center space-x-2">
                <span className="text-2xl">📱</span>
                <span>Get Our App</span>
              </h3>
              <div className="space-y-4">
                <p className="text-sm opacity-75 mb-4">
                  Download our mobile app for the best experience with live scores, notifications, and exclusive content.
                </p>

                {data.appDownloads.map((app) => (
                  <a
                    key={app.platform}
                    href={app.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`flex items-center space-x-3 p-4 rounded-lg border transition-all duration-300 hover:scale-105 ${getCardThemeClasses()}`}
                  >
                    <span className="text-2xl">{app.icon}</span>
                    <div className="flex-1">
                      <div className="font-medium text-sm">{app.label}</div>
                      <div className="text-xs opacity-75 space-x-2">
                        {app.version && <span>v{app.version}</span>}
                        {app.size && <span>• {app.size}</span>}
                        {app.rating && <span>• ⭐ {app.rating}</span>}
                      </div>
                    </div>
                  </a>
                ))}

                <div className={`p-4 rounded-lg border ${getCardThemeClasses()}`}>
                  <h4 className="font-medium text-sm mb-2">App Features:</h4>
                  <ul className="text-xs opacity-75 space-y-1">
                    <li>• Live match notifications</li>
                    <li>• Offline reading mode</li>
                    <li>• Personalized news feed</li>
                    <li>• Video highlights</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Quick Links & Company Info */}
          <div className="lg:col-span-1">
            {/* Company Info */}
            {showCompanyInfo && (
              <div className="mb-8">
                <h3 className="text-xl font-bold mb-4">{data.companyInfo.name}</h3>
                <p className="text-sm opacity-75 mb-4">{data.companyInfo.description}</p>

                {/* Social Links */}
                {showSocialLinks && (
                  <div className="flex space-x-3 mb-4">
                    {data.socialLinks.map((social) => (
                      <a
                        key={social.platform}
                        href={social.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`w-10 h-10 rounded-lg border flex items-center justify-center transition-all duration-300 hover:scale-110 ${getCardThemeClasses()}`}
                        title={`${social.label} - ${social.followers} followers`}
                      >
                        <span className="text-lg">{social.icon}</span>
                      </a>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Quick Links */}
            <div className="space-y-6">
              {data.footerSections.map((section) => (
                <div key={section.title}>
                  <h4 className="font-semibold text-sm mb-3">{section.title}</h4>
                  <ul className="space-y-2">
                    {section.links.map((link) => (
                      <li key={link.href}>
                        <Link
                          href={link.href}
                          className="text-sm opacity-75 hover:opacity-100 transition-opacity"
                        >
                          {link.label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-12 pt-8 border-t border-gray-700">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm opacity-75">
              {data.companyInfo.copyright}
            </div>
            <div className="flex items-center space-x-6 text-sm opacity-75">
              <span>{data.contactInfo.supportHours}</span>
              <span>•</span>
              <a href={`mailto:${data.contactInfo.email}`} className="hover:opacity-100 transition-opacity">
                {data.contactInfo.email}
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
