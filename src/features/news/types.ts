// News Feature Types
// This file contains all TypeScript types for the news feature

// Base News Article Interface
export interface NewsArticle {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    bio?: string;
  };
  category: {
    id: string;
    name: string;
    slug: string;
    color?: string;
  };
  tags: string[];
  publishedAt: string;
  updatedAt: string;
  featuredImage?: {
    url: string;
    alt: string;
    caption?: string;
  };
  viewCount: number;
  shareCount: number;
  readingTime: number;
  status: 'draft' | 'published' | 'archived';
  featured: boolean;
  seoTitle?: string;
  seoDescription?: string;
}

// News Category Interface
export interface NewsCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color?: string;
  icon?: string;
  parentId?: string;
  articleCount: number;
  featured: boolean;
  order: number;
}

// News API Response Interface
export interface NewsAPIResponse {
  data: NewsArticle[];
  meta: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    itemsPerPage: number;
  };
}

// News Filter Interface
export interface NewsFilter {
  category?: string;
  tags?: string[];
  author?: string;
  dateFrom?: string;
  dateTo?: string;
  featured?: boolean;
  status?: NewsArticle['status'];
  search?: string;
}

// News Sort Options
export type NewsSortOption =
  | 'publishedAt_desc'
  | 'publishedAt_asc'
  | 'viewCount_desc'
  | 'shareCount_desc'
  | 'title_asc'
  | 'title_desc';

// News Page Props Interface
export interface NewsPageProps {
  initialArticles?: NewsArticle[];
  initialCategories?: NewsCategory[];
  filters?: NewsFilter;
  sort?: NewsSortOption;
  page?: number;
}

// News Detail Page Props Interface
export interface NewsDetailPageProps {
  article: NewsArticle;
  relatedArticles?: NewsArticle[];
  categories?: NewsCategory[];
}

// News Card Props Interface
export interface NewsCardProps {
  article: NewsArticle;
  variant?: 'default' | 'featured' | 'compact' | 'minimal';
  showCategory?: boolean;
  showAuthor?: boolean;
  showDate?: boolean;
  showExcerpt?: boolean;
  showReadingTime?: boolean;
  onClick?: (article: NewsArticle) => void;
}

// Category Filter Props Interface
export interface CategoryFilterProps {
  categories: NewsCategory[];
  selectedCategory?: string;
  onCategoryChange: (categorySlug: string | undefined) => void;
  showAll?: boolean;
}

// Search Bar Props Interface
export interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

// Pagination Props Interface
export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
  showPrevNext?: boolean;
  maxVisiblePages?: number;
}

// News Service Interface
export interface NewsServiceInterface {
  getArticles(filters?: NewsFilter, sort?: NewsSortOption, page?: number): Promise<NewsAPIResponse>;
  getArticleBySlug(slug: string): Promise<NewsArticle>;
  getRelatedArticles(slug: string, limit?: number): Promise<NewsArticle[]>;
  getFeaturedArticles(limit?: number): Promise<NewsArticle[]>;
  getArticlesByCategory(categorySlug: string, page?: number): Promise<NewsAPIResponse>;
  getCategories(): Promise<NewsCategory[]>;
  incrementViewCount(articleId: string): Promise<void>;
  incrementShareCount(articleId: string): Promise<void>;
}

// News Hook Return Types
export interface UseNewsReturn {
  articles: NewsArticle[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => void;
  refresh: () => void;
  updateFilters: (filters: NewsFilter) => void;
  updateSort: (sort: NewsSortOption) => void;
  currentPage: number;
  filters: NewsFilter;
  sort: NewsSortOption;
}

export interface UseNewsFiltersReturn {
  filters: NewsFilter;
  sort: NewsSortOption;
  updateFilter: (key: keyof NewsFilter, value: any) => void;
  updateSort: (sort: NewsSortOption) => void;
  clearFilters: () => void;
  applyFilters: () => void;
  resetFilter: (key: keyof NewsFilter) => void;
  setMultipleFilters: (filters: Partial<NewsFilter>) => void;
  toggleTag: (tag: string) => void;
  setDateRange: (dateFrom?: string, dateTo?: string) => void;
  hasActiveFilters: boolean;
  filterCount: number;
  getQueryParams: () => Record<string, any>;
}

export interface UseNewsCategoriesReturn {
  categories: NewsCategory[];
  loading: boolean;
  error: string | null;
  selectedCategory: string | undefined;
  selectCategory: (slug: string | undefined) => void;
  fetchCategories: () => Promise<void>;
  getCategoryBySlug: (slug: string) => NewsCategory | undefined;
  getFeaturedCategories: () => NewsCategory[];
  getCategoriesWithCount: () => NewsCategory[];
  searchCategories: (query: string) => NewsCategory[];
  sortCategories: (sortBy?: 'name' | 'articleCount' | 'order', direction?: 'asc' | 'desc') => void;
  refresh: () => Promise<void>;
  addCategory: (category: NewsCategory) => void;
  updateCategory: (categoryId: string, updates: Partial<NewsCategory>) => void;
  removeCategory: (categoryId: string) => void;
  getStatistics: () => {
    totalCategories: number;
    featuredCategories: number;
    totalArticles: number;
    averageArticlesPerCategory: number;
  };
}

// Error Types
export interface NewsError {
  code: string;
  message: string;
  details?: any;
}

// Component State Types
export interface NewsPageState {
  articles: NewsArticle[];
  categories: NewsCategory[];
  filters: NewsFilter;
  sort: NewsSortOption;
  currentPage: number;
  totalPages: number;
  loading: boolean;
  error: string | null;
}

export interface NewsDetailPageState {
  article: NewsArticle | null;
  relatedArticles: NewsArticle[];
  loading: boolean;
  error: string | null;
}

// Event Handler Types
export type NewsEventHandler<T = void> = (event: React.MouseEvent<HTMLElement>) => T;
export type NewsChangeHandler<T> = (value: T) => void;
export type NewsSubmitHandler = (event: React.FormEvent<HTMLFormElement>) => void;

// Utility Types
export type NewsArticlePreview = Pick<NewsArticle, 'id' | 'title' | 'slug' | 'excerpt' | 'featuredImage' | 'publishedAt' | 'category' | 'readingTime'>;
export type NewsArticleSummary = Pick<NewsArticle, 'id' | 'title' | 'slug' | 'publishedAt' | 'viewCount' | 'shareCount'>;

// Version-specific Types
export interface NewsComponentVersion {
  version: string;
  name: string;
  status: 'development' | 'stable' | 'deprecated';
  releaseDate: string;
}

// Layout Types
export interface NewsLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
}
