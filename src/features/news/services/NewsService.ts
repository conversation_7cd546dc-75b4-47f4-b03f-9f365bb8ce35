'use client';

import {
  fetchNews,
  fetchFeaturedNews,
  fetchNewsByCategory,
  searchNews,
  incrementViewCount,
  incrementShareCount,
  type NewsAPIParams,
  type NewsAPIResponse
} from '@/lib/api/news';
import { fetchNewsCategories, type NewsCategoriesAPIResponse } from '@/lib/api/newsCategories';
import { NewsCacheService } from './NewsCacheService';
import {
  NewsArticle,
  NewsCategory,
  NewsFilter,
  NewsSortOption,
  NewsServiceInterface
} from '../types';
import { parseNewsError, handleErrorWithRetry } from '../utils';

/**
 * News Service - Centralized news data management with caching
 * 
 * Features:
 * - Unified API for all news operations
 * - Automatic caching with TTL
 * - Optimized data fetching strategies
 * - Error handling and fallbacks
 * - Performance monitoring
 */
export class NewsService implements NewsServiceInterface {
  private static instance: NewsService;
  private cacheService: NewsCacheService;

  private constructor() {
    this.cacheService = NewsCacheService.getInstance();
  }

  public static getInstance(): NewsService {
    if (!NewsService.instance) {
      NewsService.instance = new NewsService();
    }
    return NewsService.instance;
  }

  /**
   * Fetch articles with filters and sorting
   */
  public async getArticles(
    filters?: NewsFilter,
    sort?: NewsSortOption,
    page?: number
  ): Promise<NewsAPIResponse> {
    // Convert filters to API params
    const params: NewsAPIParams = {
      page: page || 1,
      limit: 20,
      ...filters && {
        category: filters.category,
        search: filters.search,
        author: filters.author,
        dateFrom: filters.dateFrom,
        dateTo: filters.dateTo,
        featured: filters.featured,
        status: filters.status,
        tags: filters.tags?.join(',')
      },
      sort
    };
    const cacheKey = NewsCacheService.generateArticlesKey(params);

    // Try cache first
    const cached = this.cacheService.getCachedArticles(cacheKey);
    if (cached) {
      console.log('📦 Using cached articles:', cacheKey);
      return cached;
    }

    try {
      console.log('🔄 Fetching articles from API:', params);
      const response = await fetchNews(params);

      // Cache the response
      this.cacheService.cacheArticles(cacheKey, response);

      console.log('✅ Articles fetched and cached:', response.data.length);
      return response;
    } catch (error) {
      console.error('❌ Error fetching articles:', error);
      throw error;
    }
  }

  /**
   * Get article by slug
   */
  public async getArticleBySlug(slug: string): Promise<NewsArticle> {
    try {
      console.log('🔄 Fetching article by slug:', slug);

      // Try multiple endpoints for article detail
      const endpoints = [
        `/api/news/${slug}`,
        `/api/news/articles/${slug}`,
        `/api/news/detail/${slug}`
      ];

      for (const endpoint of endpoints) {
        try {
          const response = await fetch(endpoint);
          if (response.ok) {
            const data = await response.json();
            const article = data.article || data.data || data;
            if (article) {
              console.log('✅ Article fetched successfully:', article.title);
              return article;
            }
          }
        } catch (endpointError) {
          console.warn(`Failed to fetch from ${endpoint}:`, endpointError);
          continue;
        }
      }

      throw new Error(`Article not found: ${slug}`);
    } catch (error) {
      console.error('❌ Error fetching article by slug:', error);
      throw parseNewsError(error);
    }
  }

  /**
   * Get articles by category
   */
  public async getArticlesByCategory(categorySlug: string, page?: number): Promise<NewsAPIResponse> {
    return this.getArticles({ category: categorySlug }, 'publishedAt_desc', page);
  }

  /**
   * Fetch categories with long-term caching
   */
  public async getCategories(): Promise<NewsCategory[]> {
    const cacheKey = NewsCacheService.generateCategoriesKey();

    // Try cache first (categories change rarely)
    const cached = this.cacheService.getCachedCategories(cacheKey);
    if (cached) {
      console.log('📦 Using cached categories');
      return cached;
    }

    try {
      console.log('🔄 Fetching categories from API');
      const response = await fetchNewsCategories();

      // Cache with long TTL
      this.cacheService.cacheCategories(cacheKey, response);

      console.log('✅ Categories fetched and cached:', response.data.length);
      return response.data; // Return just the data array
    } catch (error) {
      console.error('❌ Error fetching categories:', error);
      throw parseNewsError(error);
    }
  }

  /**
   * Fetch featured articles with optimization
   */
  public async getFeaturedArticles(limit: number = 5): Promise<NewsArticle[]> {
    const cacheKey = NewsCacheService.generateArticlesKey({
      page: 1,
      limit,
      category: 'featured'
    });

    // Try cache first
    const cached = this.cacheService.getCachedArticles(cacheKey);
    if (cached) {
      console.log('📦 Using cached featured articles');
      return cached;
    }

    try {
      console.log('🔄 Fetching featured articles from API');
      const response = await fetchFeaturedNews(limit);

      // Cache the response
      this.cacheService.cacheArticles(cacheKey, response);

      console.log('✅ Featured articles fetched and cached:', response.data.length);
      return response.data; // Return just the data array
    } catch (error) {
      console.error('❌ Error fetching featured articles:', error);
      throw parseNewsError(error);
    }
  }

  /**
   * Fetch related articles with caching
   */
  public async getRelatedArticles(slug: string, limit: number = 4): Promise<NewsArticle[]> {
    const cacheKey = NewsCacheService.generateRelatedKey(slug);

    // Try cache first
    const cached = this.cacheService.getCachedRelatedArticles(cacheKey);
    if (cached) {
      console.log('📦 Using cached related articles for:', slug);
      return cached;
    }

    try {
      console.log('🔄 Fetching related articles for:', slug);

      // Try multiple endpoints for related articles
      const endpoints = [
        `/api/news/related/${slug}?limit=${limit}`,
        `/api/news/articles/related/${slug}?limit=${limit}`,
        `/api/news/similar/${slug}?limit=${limit}`
      ];

      let relatedArticles: NewsArticle[] = [];

      for (const endpoint of endpoints) {
        try {
          const response = await fetch(endpoint);
          if (response.ok) {
            const data = await response.json();
            relatedArticles = data.articles || data.data || [];
            if (relatedArticles.length > 0) {
              break;
            }
          }
        } catch (endpointError) {
          console.warn(`Failed to fetch from ${endpoint}:`, endpointError);
          continue;
        }
      }

      // If no related articles found, use mock data
      if (relatedArticles.length === 0) {
        console.log('📰 Using mock related articles for development');
        relatedArticles = this.generateMockRelatedArticles(slug, limit);
      }

      // Cache the response
      this.cacheService.cacheRelatedArticles(cacheKey, relatedArticles);

      console.log('✅ Related articles fetched and cached:', relatedArticles.length);
      return relatedArticles;
    } catch (error) {
      console.error('❌ Error fetching related articles:', error);

      // Return mock data as fallback
      return this.generateMockRelatedArticles(slug, limit);
    }
  }

  /**
   * Search articles with caching
   */
  public async searchArticles(query: string, params: Omit<NewsAPIParams, 'search'> = {}): Promise<NewsAPIResponse> {
    const searchParams = { ...params, search: query };
    return this.getArticles(searchParams);
  }

  /**
   * Get articles by category using dedicated category endpoint
   */
  public async getArticlesByCategory(category: string, params: Omit<NewsAPIParams, 'category'> = {}): Promise<NewsAPIResponse> {
    const cacheKey = NewsCacheService.generateArticlesKey({ ...params, category });

    // Try cache first
    const cached = this.cacheService.getCachedArticles(cacheKey);
    if (cached) {
      console.log('📦 Using cached category articles:', category);
      return cached;
    }

    try {
      console.log('🔄 Fetching articles by category from API:', category);
      const response = await fetchNewsByCategory(category, params.limit || 20);

      // Cache the response
      this.cacheService.cacheArticles(cacheKey, response);

      console.log('✅ Category articles fetched and cached:', response.data.length);
      return response;
    } catch (error) {
      console.error('❌ Error fetching articles by category:', error);

      // Fallback to regular articles with category filter
      console.log('🔄 Falling back to regular articles with category filter');
      const categoryParams = { ...params, category };
      return this.getArticles(categoryParams);
    }
  }

  /**
   * Generate mock related articles for development
   */
  private generateMockRelatedArticles(slug: string, limit: number): NewsArticle[] {
    const mockArticles: NewsArticle[] = [];

    for (let i = 0; i < limit; i++) {
      mockArticles.push({
        id: Date.now() + i,
        title: `Related Article ${i + 1} for ${slug}`,
        slug: `related-article-${i + 1}-${slug}`,
        excerpt: `This is a mock related article excerpt for development purposes. Article ${i + 1}.`,
        content: `Mock content for related article ${i + 1}`,
        featuredImage: null,
        publishedAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
        tags: ['related', 'mock', 'development'],
        status: 'published' as const,
        metaTitle: null,
        metaDescription: null,
        relatedLeagueId: null,
        relatedTeamId: null,
        relatedPlayerId: null,
        relatedFixtureId: null,
        viewCount: Math.floor(Math.random() * 1000),
        shareCount: Math.floor(Math.random() * 100),
        likeCount: Math.floor(Math.random() * 50),
        isFeatured: false,
        priority: 5,
        categoryId: 1,
        authorId: 1,
        category: {
          id: 1,
          slug: 'general',
          name: 'General',
          description: 'General news',
          icon: 'general',
          color: '#6B7280',
          sortOrder: 1,
          isActive: true,
          isPublic: true,
          metaTitle: null,
          metaDescription: null,
          articleCount: 10,
          publishedArticleCount: 8
        }
      });
    }

    return mockArticles;
  }

  /**
   * Search articles with enhanced error handling
   */
  public async searchArticles(query: string, filters?: NewsFilter, page?: number): Promise<NewsAPIResponse> {
    const searchFilters = { ...filters, search: query };
    return this.getArticles(searchFilters, 'relevance_desc', page);
  }

  /**
   * Increment view count (interface method)
   */
  public async incrementViewCount(articleId: string): Promise<void> {
    try {
      await this.incrementArticleViewCount(articleId);
    } catch (error) {
      console.error('Error incrementing view count:', error);
      // Don't throw error for analytics operations
    }
  }

  /**
   * Increment share count (interface method)
   */
  public async incrementShareCount(articleId: string): Promise<void> {
    try {
      await this.incrementArticleShareCount(articleId);
    } catch (error) {
      console.error('Error incrementing share count:', error);
      // Don't throw error for analytics operations
    }
  }

  /**
   * Increment article view count (internal method)
   */
  public async incrementArticleViewCount(articleId: string): Promise<{ success: boolean; viewCount: number }> {
    try {
      console.log('🔄 Incrementing view count for article:', articleId);
      const result = await incrementViewCount(articleId);

      // Invalidate related caches
      this.invalidateArticleCache(articleId);

      console.log('✅ View count incremented successfully:', result.viewCount);
      return result;
    } catch (error) {
      console.error('❌ Error incrementing view count:', error);
      return { success: false, viewCount: 0 };
    }
  }

  /**
   * Increment article share count
   */
  public async incrementArticleShareCount(articleId: string): Promise<{ success: boolean; shareCount: number }> {
    try {
      console.log('🔄 Incrementing share count for article:', articleId);
      const result = await incrementShareCount(articleId);

      // Invalidate related caches
      this.invalidateArticleCache(articleId);

      console.log('✅ Share count incremented successfully:', result.shareCount);
      return result;
    } catch (error) {
      console.error('❌ Error incrementing share count:', error);
      return { success: false, shareCount: 0 };
    }
  }

  /**
   * Invalidate caches related to a specific article
   */
  private invalidateArticleCache(articleId: string): void {
    // This is a simple implementation - in a more sophisticated system,
    // we might track which cache keys are related to which articles
    console.log('🗑️ Invalidating caches for article:', articleId);

    // For now, we'll just clear all article caches to ensure consistency
    // In production, you might want to be more selective
    this.cacheService.clearAll();
  }

  /**
   * Clear all caches
   */
  public clearCache(): void {
    this.cacheService.clearAll();
  }

  /**
   * Get cache statistics
   */
  public getCacheStats() {
    return this.cacheService.getStats();
  }
}

// Export singleton instance
export const newsService = NewsService.getInstance();
