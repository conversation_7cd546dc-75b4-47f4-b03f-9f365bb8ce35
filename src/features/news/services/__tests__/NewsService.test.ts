// NewsService Integration Tests
import { NewsService } from '../NewsService';
import { NewsFilter, NewsSortOption } from '../../types';

// Mock the API modules
jest.mock('@/lib/api/news', () => ({
  fetchNews: jest.fn(),
  fetchFeaturedNews: jest.fn(),
  fetchNewsByCategory: jest.fn(),
  searchNews: jest.fn(),
  incrementViewCount: jest.fn(),
  incrementShareCount: jest.fn()
}));

jest.mock('@/lib/api/newsCategories', () => ({
  fetchNewsCategories: jest.fn()
}));

jest.mock('../NewsCacheService', () => ({
  NewsCacheService: {
    getInstance: jest.fn(() => ({
      getCachedArticles: jest.fn(),
      cacheArticles: jest.fn(),
      getCachedCategories: jest.fn(),
      cacheCategories: jest.fn(),
      getCachedRelatedArticles: jest.fn(),
      cacheRelatedArticles: jest.fn(),
      clearAll: jest.fn(),
      getStats: jest.fn(() => ({ hits: 0, misses: 0, size: 0 }))
    })),
    generateArticlesKey: jest.fn((params) => `articles_${JSON.stringify(params)}`),
    generateCategoriesKey: jest.fn(() => 'categories'),
    generateRelatedKey: jest.fn((slug) => `related_${slug}`)
  }
}));

// Mock fetch for related articles
global.fetch = jest.fn();

describe('NewsService Integration Tests', () => {
  let newsService: NewsService;
  let mockFetchNews: jest.Mock;
  let mockFetchCategories: jest.Mock;
  let mockFetchFeaturedNews: jest.Mock;
  let mockIncrementViewCount: jest.Mock;
  let mockIncrementShareCount: jest.Mock;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Get fresh instance
    newsService = NewsService.getInstance();
    
    // Setup API mocks
    const newsApi = require('@/lib/api/news');
    const categoriesApi = require('@/lib/api/newsCategories');
    
    mockFetchNews = newsApi.fetchNews;
    mockFetchFeaturedNews = newsApi.fetchFeaturedNews;
    mockIncrementViewCount = newsApi.incrementViewCount;
    mockIncrementShareCount = newsApi.incrementShareCount;
    mockFetchCategories = categoriesApi.fetchNewsCategories;
  });

  describe('Article Fetching', () => {
    it('should fetch articles with filters and sorting', async () => {
      const mockResponse = {
        data: [
          {
            id: '1',
            title: 'Test Article',
            slug: 'test-article',
            excerpt: 'Test excerpt',
            content: 'Test content',
            publishedAt: '2024-01-25T00:00:00Z',
            category: { id: '1', name: 'Sports', slug: 'sports' },
            author: { id: '1', name: 'Test Author' },
            tags: ['test'],
            viewCount: 100,
            shareCount: 10
          }
        ],
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      };

      mockFetchNews.mockResolvedValue(mockResponse);

      const filters: NewsFilter = {
        category: 'sports',
        search: 'test'
      };
      const sort: NewsSortOption = 'publishedAt_desc';

      const result = await newsService.getArticles(filters, sort, 1);

      expect(mockFetchNews).toHaveBeenCalledWith({
        page: 1,
        limit: 20,
        category: 'sports',
        search: 'test',
        sort: 'publishedAt_desc'
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors gracefully', async () => {
      const error = new Error('API Error');
      mockFetchNews.mockRejectedValue(error);

      await expect(newsService.getArticles()).rejects.toThrow('API Error');
    });

    it('should fetch article by slug', async () => {
      const mockArticle = {
        id: '1',
        title: 'Test Article',
        slug: 'test-article',
        content: 'Full content',
        publishedAt: '2024-01-25T00:00:00Z'
      };

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ article: mockArticle })
      });

      const result = await newsService.getArticleBySlug('test-article');

      expect(global.fetch).toHaveBeenCalledWith('/api/news/test-article');
      expect(result).toEqual(mockArticle);
    });

    it('should try multiple endpoints for article by slug', async () => {
      const mockArticle = {
        id: '1',
        title: 'Test Article',
        slug: 'test-article'
      };

      // First endpoint fails, second succeeds
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: false,
          status: 404
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ data: mockArticle })
        });

      const result = await newsService.getArticleBySlug('test-article');

      expect(global.fetch).toHaveBeenCalledTimes(2);
      expect(global.fetch).toHaveBeenNthCalledWith(1, '/api/news/test-article');
      expect(global.fetch).toHaveBeenNthCalledWith(2, '/api/news/articles/test-article');
      expect(result).toEqual(mockArticle);
    });
  });

  describe('Categories', () => {
    it('should fetch categories', async () => {
      const mockCategories = {
        data: [
          {
            id: '1',
            name: 'Sports',
            slug: 'sports',
            articleCount: 10
          },
          {
            id: '2',
            name: 'News',
            slug: 'news',
            articleCount: 5
          }
        ]
      };

      mockFetchCategories.mockResolvedValue(mockCategories);

      const result = await newsService.getCategories();

      expect(mockFetchCategories).toHaveBeenCalled();
      expect(result).toEqual(mockCategories.data);
    });

    it('should handle category fetch errors', async () => {
      const error = new Error('Categories API Error');
      mockFetchCategories.mockRejectedValue(error);

      await expect(newsService.getCategories()).rejects.toThrow();
    });
  });

  describe('Featured Articles', () => {
    it('should fetch featured articles', async () => {
      const mockFeatured = {
        data: [
          {
            id: '1',
            title: 'Featured Article',
            slug: 'featured-article',
            isFeatured: true
          }
        ]
      };

      mockFetchFeaturedNews.mockResolvedValue(mockFeatured);

      const result = await newsService.getFeaturedArticles(5);

      expect(mockFetchFeaturedNews).toHaveBeenCalledWith(5);
      expect(result).toEqual(mockFeatured.data);
    });
  });

  describe('Related Articles', () => {
    it('should fetch related articles', async () => {
      const mockRelated = [
        {
          id: '2',
          title: 'Related Article 1',
          slug: 'related-1'
        },
        {
          id: '3',
          title: 'Related Article 2',
          slug: 'related-2'
        }
      ];

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ articles: mockRelated })
      });

      const result = await newsService.getRelatedArticles('test-article', 4);

      expect(global.fetch).toHaveBeenCalledWith('/api/news/related/test-article?limit=4');
      expect(result).toEqual(mockRelated);
    });

    it('should fallback to mock data when API fails', async () => {
      (global.fetch as jest.Mock).mockRejectedValue(new Error('API Error'));

      const result = await newsService.getRelatedArticles('test-article', 3);

      expect(result).toHaveLength(3);
      expect(result[0]).toHaveProperty('title');
      expect(result[0].title).toContain('Related Article 1 for test-article');
    });
  });

  describe('Analytics', () => {
    it('should increment view count', async () => {
      const mockResult = { success: true, viewCount: 101 };
      mockIncrementViewCount.mockResolvedValue(mockResult);

      const result = await newsService.incrementArticleViewCount('article-1');

      expect(mockIncrementViewCount).toHaveBeenCalledWith('article-1');
      expect(result).toEqual(mockResult);
    });

    it('should increment share count', async () => {
      const mockResult = { success: true, shareCount: 11 };
      mockIncrementShareCount.mockResolvedValue(mockResult);

      const result = await newsService.incrementArticleShareCount('article-1');

      expect(mockIncrementShareCount).toHaveBeenCalledWith('article-1');
      expect(result).toEqual(mockResult);
    });

    it('should handle analytics errors gracefully', async () => {
      mockIncrementViewCount.mockRejectedValue(new Error('Analytics Error'));

      const result = await newsService.incrementArticleViewCount('article-1');

      expect(result).toEqual({ success: false, viewCount: 0 });
    });

    it('should not throw errors for interface methods', async () => {
      mockIncrementViewCount.mockRejectedValue(new Error('Analytics Error'));

      // Should not throw
      await expect(newsService.incrementViewCount('article-1')).resolves.toBeUndefined();
    });
  });

  describe('Search', () => {
    it('should search articles with filters', async () => {
      const mockResponse = {
        data: [
          {
            id: '1',
            title: 'Search Result',
            slug: 'search-result'
          }
        ],
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      };

      mockFetchNews.mockResolvedValue(mockResponse);

      const filters: NewsFilter = { category: 'sports' };
      const result = await newsService.searchArticles('test query', filters, 1);

      expect(mockFetchNews).toHaveBeenCalledWith({
        page: 1,
        limit: 20,
        category: 'sports',
        search: 'test query',
        sort: 'relevance_desc'
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Cache Management', () => {
    it('should clear cache', () => {
      const mockCacheService = require('../NewsCacheService').NewsCacheService.getInstance();
      
      newsService.clearCache();

      expect(mockCacheService.clearAll).toHaveBeenCalled();
    });

    it('should get cache stats', () => {
      const mockStats = { hits: 10, misses: 5, size: 15 };
      const mockCacheService = require('../NewsCacheService').NewsCacheService.getInstance();
      mockCacheService.getStats.mockReturnValue(mockStats);

      const stats = newsService.getCacheStats();

      expect(stats).toEqual(mockStats);
    });
  });

  describe('Error Handling', () => {
    it('should parse and throw proper errors', async () => {
      const apiError = {
        message: 'Not Found',
        code: 'NOT_FOUND',
        status: 404
      };
      
      mockFetchNews.mockRejectedValue(apiError);

      await expect(newsService.getArticles()).rejects.toThrow('Not Found');
    });
  });
});
