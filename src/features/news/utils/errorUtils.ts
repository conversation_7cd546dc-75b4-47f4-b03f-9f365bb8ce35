// News Error Utilities
// Utilities for handling news-related errors and error messages

/**
 * News error types
 */
export interface NewsError {
  code: string;
  message: string;
  details?: any;
  statusCode?: number;
}

/**
 * Error codes for news operations
 */
export const NEWS_ERROR_CODES = {
  ARTICLE_NOT_FOUND: 'ARTICLE_NOT_FOUND',
  CATEGORY_NOT_FOUND: 'CATEGORY_NOT_FOUND',
  NETWORK_ERROR: 'NETWORK_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  SERVER_ERROR: 'SERVER_ERROR',
  TIMEOUT: 'TIMEOUT',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  CLIENT_ERROR: 'CLIENT_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  API_ERROR: 'API_ERROR',
  UNKNOWN: 'UNKNOWN'
} as const;

/**
 * Parse error from API response or thrown error
 * @param error - Error object or response
 * @returns Parsed NewsError
 */
export function parseNewsError(error: any): NewsError {
  // If it's already a NewsError, return as is
  if (error && typeof error === 'object' && error.code && error.message) {
    return error as NewsError;
  }

  // Handle fetch/network errors
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return {
      code: NEWS_ERROR_CODES.NETWORK_ERROR,
      message: 'Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng.',
      details: error.message
    };
  }

  // Handle HTTP response errors
  if (error && error.response) {
    const status = error.response.status;
    const data = error.response.data;

    switch (status) {
      case 404:
        return {
          code: NEWS_ERROR_CODES.ARTICLE_NOT_FOUND,
          message: data?.message || 'Không tìm thấy bài viết.',
          statusCode: status,
          details: data
        };
      case 401:
        return {
          code: NEWS_ERROR_CODES.UNAUTHORIZED,
          message: 'Bạn cần đăng nhập để thực hiện thao tác này.',
          statusCode: status,
          details: data
        };
      case 403:
        return {
          code: NEWS_ERROR_CODES.FORBIDDEN,
          message: 'Bạn không có quyền truy cập nội dung này.',
          statusCode: status,
          details: data
        };
      case 422:
        return {
          code: NEWS_ERROR_CODES.VALIDATION_ERROR,
          message: data?.message || 'Dữ liệu không hợp lệ.',
          statusCode: status,
          details: data
        };
      case 500:
      case 502:
      case 503:
      case 504:
        return {
          code: NEWS_ERROR_CODES.SERVER_ERROR,
          message: 'Máy chủ đang gặp sự cố. Vui lòng thử lại sau.',
          statusCode: status,
          details: data
        };
      default:
        return {
          code: NEWS_ERROR_CODES.UNKNOWN,
          message: data?.message || 'Đã xảy ra lỗi không xác định.',
          statusCode: status,
          details: data
        };
    }
  }

  // Handle timeout errors
  if (error && error.code === 'ECONNABORTED') {
    return {
      code: NEWS_ERROR_CODES.TIMEOUT,
      message: 'Yêu cầu đã hết thời gian chờ. Vui lòng thử lại.',
      details: error.message
    };
  }

  // Handle generic Error objects
  if (error instanceof Error) {
    return {
      code: NEWS_ERROR_CODES.CLIENT_ERROR,
      message: error.message || 'Đã xảy ra lỗi không xác định.',
      details: error.stack
    };
  }

  // Handle string errors
  if (typeof error === 'string') {
    return {
      code: NEWS_ERROR_CODES.UNKNOWN_ERROR,
      message: error
    };
  }

  // Fallback for unknown error types
  return {
    code: NEWS_ERROR_CODES.UNKNOWN,
    message: 'Đã xảy ra lỗi không xác định.',
    details: error
  };
}

/**
 * Get user-friendly error message
 * @param error - NewsError or any error
 * @returns User-friendly error message
 */
export function getUserFriendlyErrorMessage(error: any): string {
  const newsError = parseNewsError(error);

  // Provide user-friendly messages for specific error codes
  switch (newsError.code) {
    case NEWS_ERROR_CODES.TIMEOUT_ERROR:
    case NEWS_ERROR_CODES.TIMEOUT:
      return 'The request is taking too long. Please try again.';
    case NEWS_ERROR_CODES.NETWORK_ERROR:
      return 'There was a connection error. Please check your internet connection.';
    case NEWS_ERROR_CODES.ARTICLE_NOT_FOUND:
    case NEWS_ERROR_CODES.NOT_FOUND:
      return 'The requested content was not found.';
    case NEWS_ERROR_CODES.UNAUTHORIZED:
      return 'You need to log in to access this content.';
    case NEWS_ERROR_CODES.FORBIDDEN:
      return 'You do not have permission to access this content.';
    case NEWS_ERROR_CODES.SERVER_ERROR:
      return 'The server is experiencing issues. Please try again later.';
    case NEWS_ERROR_CODES.VALIDATION_ERROR:
      return 'The provided data is invalid. Please check your input.';
    default:
      // Return the original message for other cases
      return newsError.message;
  }
}

/**
 * Check if error is a network error
 * @param error - Error to check
 * @returns True if it's a network error
 */
export function isNetworkError(error: any): boolean {
  const newsError = parseNewsError(error);
  return newsError.code === NEWS_ERROR_CODES.NETWORK_ERROR;
}

/**
 * Check if error is a not found error
 * @param error - Error to check
 * @returns True if it's a not found error
 */
export function isNotFoundError(error: any): boolean {
  const newsError = parseNewsError(error);
  return newsError.code === NEWS_ERROR_CODES.ARTICLE_NOT_FOUND ||
    newsError.code === NEWS_ERROR_CODES.CATEGORY_NOT_FOUND;
}

/**
 * Check if error is an authorization error
 * @param error - Error to check
 * @returns True if it's an authorization error
 */
export function isAuthError(error: any): boolean {
  const newsError = parseNewsError(error);
  return newsError.code === NEWS_ERROR_CODES.UNAUTHORIZED ||
    newsError.code === NEWS_ERROR_CODES.FORBIDDEN;
}

/**
 * Check if error is a server error
 * @param error - Error to check
 * @returns True if it's a server error
 */
export function isServerError(error: any): boolean {
  const newsError = parseNewsError(error);
  return newsError.code === NEWS_ERROR_CODES.SERVER_ERROR;
}

/**
 * Check if error is retryable
 * @param error - Error to check
 * @returns True if the operation can be retried
 */
export function isRetryableError(error: any): boolean {
  const newsError = parseNewsError(error);
  return newsError.code === NEWS_ERROR_CODES.NETWORK_ERROR ||
    newsError.code === NEWS_ERROR_CODES.TIMEOUT ||
    newsError.code === NEWS_ERROR_CODES.TIMEOUT_ERROR ||
    newsError.code === NEWS_ERROR_CODES.SERVER_ERROR;
}

/**
 * Create a custom news error
 * @param code - Error code
 * @param message - Error message
 * @param details - Additional error details
 * @returns NewsError object
 */
export function createNewsError(
  code: keyof typeof NEWS_ERROR_CODES,
  message: string,
  details?: any
): NewsError {
  return {
    code: NEWS_ERROR_CODES[code],
    message,
    details
  };
}

/**
 * Log error with context
 * @param error - Error to log
 * @param context - Additional context
 */
export function logNewsError(error: any, context?: string): void {
  const newsError = parseNewsError(error);

  console.error(`[News Error${context ? ` - ${context}` : ''}]:`, {
    code: newsError.code,
    message: newsError.message,
    details: newsError.details,
    timestamp: new Date().toISOString()
  });
}

/**
 * Handle error with retry logic
 * @param error - Error that occurred
 * @param retryFn - Function to retry
 * @param maxRetries - Maximum number of retries
 * @param retryDelay - Delay between retries in ms
 * @returns Promise that resolves when retry succeeds or rejects when max retries reached
 */
export async function handleErrorWithRetry<T>(
  error: any,
  retryFn: () => Promise<T>,
  maxRetries: number = 3,
  retryDelay: number = 1000
): Promise<T> {
  if (!isRetryableError(error) || maxRetries <= 0) {
    throw error;
  }

  await new Promise(resolve => setTimeout(resolve, retryDelay));

  try {
    return await retryFn();
  } catch (retryError) {
    return handleErrorWithRetry(retryError, retryFn, maxRetries - 1, retryDelay * 2);
  }
}
