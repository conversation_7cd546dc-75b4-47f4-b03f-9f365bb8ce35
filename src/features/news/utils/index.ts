// News Utils Exports
// This file manages all utility exports for the news feature

// Image utilities
export * from './imageUtils';

// Error utilities
export * from './errorUtils';

// Category utilities
export * from './categoryUtils';

// API utilities
export * from './apiUtils';

// Re-export commonly used functions
export {
  getImageUrl,
  handleImageError,
  optimizeImageUrl,
  getResponsiveImageSources,
  preloadImages,
  isImageValid,
  getImageDimensions,
  generatePlaceholder,
  getOptimizedImageProps,
  IMAGE_CONFIG
} from './imageUtils';

export {
  parseNewsError,
  getUserFriendlyErrorMessage,
  isNetworkError,
  isNotFoundError,
  isAuthError,
  isServerError,
  isRetryableError,
  createNewsError,
  logNewsError,
  handleErrorWithRetry,
  NEWS_ERROR_CODES
} from './errorUtils';

export type { NewsError } from './errorUtils';
