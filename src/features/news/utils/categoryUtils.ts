// News Category Utilities
// Utilities for handling news categories and category-related operations

import { NewsCategory } from '../types';

/**
 * Sort categories by order and name
 * @param categories - Array of categories to sort
 * @returns Sorted categories
 */
export function sortCategories(categories: NewsCategory[]): NewsCategory[] {
  return [...categories].sort((a, b) => {
    // First sort by order (ascending)
    if (a.order !== b.order) {
      return a.order - b.order;
    }
    // Then sort by name (alphabetical)
    return a.name.localeCompare(b.name, 'vi');
  });
}

/**
 * Filter categories by featured status
 * @param categories - Array of categories
 * @param featuredOnly - Whether to return only featured categories
 * @returns Filtered categories
 */
export function filterFeaturedCategories(
  categories: NewsCategory[],
  featuredOnly: boolean = true
): NewsCategory[] {
  if (!featuredOnly) {
    return categories;
  }
  return categories.filter(category => category.featured);
}

/**
 * Get category by slug
 * @param categories - Array of categories
 * @param slug - Category slug to find
 * @returns Found category or undefined
 */
export function getCategoryBySlug(
  categories: NewsCategory[],
  slug: string
): NewsCategory | undefined {
  return categories.find(category => category.slug === slug);
}

/**
 * Get category by ID
 * @param categories - Array of categories
 * @param id - Category ID to find
 * @returns Found category or undefined
 */
export function getCategoryById(
  categories: NewsCategory[],
  id: string
): NewsCategory | undefined {
  return categories.find(category => category.id === id);
}

/**
 * Build category hierarchy (parent-child relationships)
 * @param categories - Flat array of categories
 * @returns Hierarchical category structure
 */
export function buildCategoryHierarchy(categories: NewsCategory[]): NewsCategory[] {
  const categoryMap = new Map<string, NewsCategory & { children?: NewsCategory[] }>();
  const rootCategories: (NewsCategory & { children?: NewsCategory[] })[] = [];

  // Create a map of all categories
  categories.forEach(category => {
    categoryMap.set(category.id, { ...category, children: [] });
  });

  // Build hierarchy
  categories.forEach(category => {
    const categoryWithChildren = categoryMap.get(category.id)!;

    if (category.parentId) {
      const parent = categoryMap.get(category.parentId);
      if (parent) {
        parent.children!.push(categoryWithChildren);
      } else {
        // Parent not found, treat as root
        rootCategories.push(categoryWithChildren);
      }
    } else {
      rootCategories.push(categoryWithChildren);
    }
  });

  return rootCategories;
}

/**
 * Get all child categories of a parent category
 * @param categories - Array of categories
 * @param parentId - Parent category ID
 * @returns Array of child categories
 */
export function getChildCategories(
  categories: NewsCategory[],
  parentId: string
): NewsCategory[] {
  return categories.filter(category => category.parentId === parentId);
}

/**
 * Get category breadcrumb path
 * @param categories - Array of categories
 * @param categoryId - Target category ID
 * @returns Array of categories from root to target
 */
export function getCategoryBreadcrumb(
  categories: NewsCategory[],
  categoryId: string
): NewsCategory[] {
  const breadcrumb: NewsCategory[] = [];
  const categoryMap = new Map(categories.map(cat => [cat.id, cat]));

  let currentCategory = categoryMap.get(categoryId);

  while (currentCategory) {
    breadcrumb.unshift(currentCategory);
    currentCategory = currentCategory.parentId
      ? categoryMap.get(currentCategory.parentId)
      : undefined;
  }

  return breadcrumb;
}

/**
 * Generate category color if not provided
 * @param categoryName - Category name
 * @param existingColor - Existing color (if any)
 * @returns Color hex code
 */
export function generateCategoryColor(
  categoryName: string,
  existingColor?: string
): string {
  if (existingColor) {
    return existingColor;
  }

  // Predefined colors for common categories
  const colorMap: Record<string, string> = {
    'bóng đá': '#10B981', // green
    'thể thao': '#3B82F6', // blue
    'tin tức': '#6366F1', // indigo
    'giải trí': '#8B5CF6', // purple
    'công nghệ': '#06B6D4', // cyan
    'kinh tế': '#F59E0B', // amber
    'chính trị': '#EF4444', // red
    'xã hội': '#84CC16', // lime
    'văn hóa': '#EC4899', // pink
    'giáo dục': '#14B8A6', // teal
  };

  const normalizedName = categoryName.toLowerCase().trim();

  // Check for exact match
  if (colorMap[normalizedName]) {
    return colorMap[normalizedName];
  }

  // Check for partial match
  for (const [key, color] of Object.entries(colorMap)) {
    if (normalizedName.includes(key) || key.includes(normalizedName)) {
      return color;
    }
  }

  // Generate color based on category name hash
  let hash = 0;
  for (let i = 0; i < categoryName.length; i++) {
    hash = categoryName.charCodeAt(i) + ((hash << 5) - hash);
  }

  // Convert hash to HSL color
  const hue = Math.abs(hash) % 360;
  const saturation = 60 + (Math.abs(hash) % 20); // 60-80%
  const lightness = 45 + (Math.abs(hash) % 10); // 45-55%

  return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
}

/**
 * Validate category data
 * @param category - Category to validate
 * @returns Validation result
 */
export function validateCategory(category: Partial<NewsCategory>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!category.name || category.name.trim().length === 0) {
    errors.push('Tên danh mục không được để trống');
  }

  if (!category.slug || category.slug.trim().length === 0) {
    errors.push('Slug danh mục không được để trống');
  }

  if (category.slug && !/^[a-z0-9-]+$/.test(category.slug)) {
    errors.push('Slug chỉ được chứa chữ thường, số và dấu gạch ngang');
  }

  if (category.order !== undefined && category.order < 0) {
    errors.push('Thứ tự danh mục phải là số không âm');
  }

  if (category.articleCount !== undefined && category.articleCount < 0) {
    errors.push('Số lượng bài viết phải là số không âm');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Create category slug from name
 * @param name - Category name
 * @returns URL-friendly slug
 */
export function createCategorySlug(name: string): string {
  return name
    .toLowerCase()
    .trim()
    .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a')
    .replace(/[èéẹẻẽêềếệểễ]/g, 'e')
    .replace(/[ìíịỉĩ]/g, 'i')
    .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o')
    .replace(/[ùúụủũưừứựửữ]/g, 'u')
    .replace(/[ỳýỵỷỹ]/g, 'y')
    .replace(/đ/g, 'd')
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Get category statistics
 * @param categories - Array of categories
 * @returns Category statistics
 */
export function getCategoryStatistics(categories: NewsCategory[]) {
  const totalCategories = categories.length;
  const featuredCategories = categories.filter(cat => cat.featured).length;
  const totalArticles = categories.reduce((sum, cat) => sum + cat.articleCount, 0);
  const categoriesWithParent = categories.filter(cat => cat.parentId).length;
  const rootCategories = categories.filter(cat => !cat.parentId).length;

  return {
    totalCategories,
    featuredCategories,
    totalArticles,
    categoriesWithParent,
    rootCategories,
    averageArticlesPerCategory: totalCategories > 0 ? totalArticles / totalCategories : 0
  };
}

/**
 * Search categories by name or description
 * @param categories - Array of categories
 * @param query - Search query
 * @returns Filtered categories
 */
export function searchCategories(
  categories: NewsCategory[],
  query: string
): NewsCategory[] {
  if (!query.trim()) {
    return categories;
  }

  const normalizedQuery = query.toLowerCase().trim();

  return categories.filter(category => {
    const nameMatch = category.name.toLowerCase().includes(normalizedQuery);
    const descriptionMatch = category.description?.toLowerCase().includes(normalizedQuery);
    const slugMatch = category.slug.toLowerCase().includes(normalizedQuery);

    return nameMatch || descriptionMatch || slugMatch;
  });
}

/**
 * Merge API categories with "All News" category
 * @param apiCategories - Categories from API
 * @returns Merged categories with "All" option
 */
export function mergeCategories(apiCategories: NewsCategory[]): NewsCategory[] {
  const allCategory: NewsCategory = {
    id: 'all',
    name: 'All News',
    slug: 'all',
    description: 'All news and articles',
    articleCount: apiCategories.reduce((sum, cat) => sum + cat.articleCount, 0),
    featured: true,
    order: 0
  };

  return [allCategory, ...sortCategories(apiCategories)];
}

/**
 * Get category icon based on category type
 * @param categorySlug - Category slug
 * @returns Icon emoji or symbol
 */
export function getCategoryIcon(categorySlug: string): string {
  const iconMap: Record<string, string> = {
    'match': '⚽',
    'breaking': '🔥',
    'football': '⚽',
    'soccer': '⚽',
    'sports': '🏆',
    'news': '📰',
    'entertainment': '🎭',
    'technology': '💻',
    'business': '💼',
    'politics': '🏛️',
    'health': '🏥',
    'science': '🔬',
    'education': '📚',
    'culture': '🎨',
    'travel': '✈️',
    'food': '🍽️',
    'lifestyle': '🌟',
    'opinion': '💭',
    'analysis': '📊',
    'interview': '🎤'
  };

  const normalizedSlug = categorySlug.toLowerCase().trim();

  // Check for exact match
  if (iconMap[normalizedSlug]) {
    return iconMap[normalizedSlug];
  }

  // Check for partial match
  for (const [key, icon] of Object.entries(iconMap)) {
    if (normalizedSlug.includes(key) || key.includes(normalizedSlug)) {
      return icon;
    }
  }

  // Default fallback
  return '📝';
}

/**
 * Get category colors based on category type
 * @param categorySlug - Category slug
 * @returns CSS color class or hex color
 */
export function getCategoryColors(categorySlug: string): string {
  const colorMap: Record<string, string> = {
    'breaking': 'red-500',
    'match': 'blue-500',
    'football': 'green-500',
    'soccer': 'green-500',
    'sports': 'blue-600',
    'news': 'gray-600',
    'entertainment': 'purple-500',
    'technology': 'cyan-500',
    'business': 'amber-500',
    'politics': 'red-600',
    'health': 'teal-500',
    'science': 'indigo-500',
    'education': 'emerald-500',
    'culture': 'pink-500',
    'travel': 'sky-500',
    'food': 'orange-500',
    'lifestyle': 'violet-500',
    'opinion': 'slate-500',
    'analysis': 'stone-500',
    'interview': 'rose-500'
  };

  const normalizedSlug = categorySlug.toLowerCase().trim();

  // Check for exact match
  if (colorMap[normalizedSlug]) {
    return colorMap[normalizedSlug];
  }

  // Check for partial match
  for (const [key, color] of Object.entries(colorMap)) {
    if (normalizedSlug.includes(key) || key.includes(normalizedSlug)) {
      return color;
    }
  }

  // Default fallback
  return 'gray-500';
}

/**
 * Format category display name with optional article count
 * @param category - Category object or object with count property
 * @param showCount - Whether to show article count
 * @returns Formatted display name
 */
export function formatCategoryDisplayName(
  category: NewsCategory | (Partial<NewsCategory> & { count?: number }),
  showCount: boolean = false
): string {
  if (showCount) {
    const count = (category as any).count || category.articleCount || 0;
    if (count > 0) {
      return `${category.name} (${count})`;
    }
  }
  return category.name || '';
}
