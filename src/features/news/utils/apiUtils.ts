// News API Utilities
// Utilities for handling API requests, query parameters, and response processing

/**
 * Parse query parameters from URLSearchParams
 * @param searchParams - URLSearchParams object
 * @returns Parsed parameters object
 */
export function parseQueryParams(searchParams: URLSearchParams): {
  page: number;
  limit: number;
  category?: string;
  search?: string;
  sort?: string;
  author?: string;
  dateFrom?: string;
  dateTo?: string;
  featured?: boolean;
} {
  const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10) || 1);
  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '20', 10) || 20));
  
  const result: any = { page, limit };
  
  // Optional string parameters
  const stringParams = ['category', 'search', 'sort', 'author', 'dateFrom', 'dateTo'];
  stringParams.forEach(param => {
    const value = searchParams.get(param);
    if (value && value.trim()) {
      result[param] = value.trim();
    }
  });
  
  // Boolean parameters
  const featuredParam = searchParams.get('featured');
  if (featuredParam !== null) {
    result.featured = featuredParam === 'true' || featuredParam === '1';
  }
  
  return result;
}

/**
 * Build query string from parameters object
 * @param params - Parameters object
 * @returns Query string
 */
export function buildQueryString(params: Record<string, any>): string {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    // Skip null, undefined, or empty string values
    if (value !== null && value !== undefined && value !== '') {
      searchParams.append(key, String(value));
    }
  });
  
  return searchParams.toString();
}

/**
 * Build API URL with query parameters
 * @param baseUrl - Base API URL
 * @param params - Query parameters
 * @returns Complete API URL
 */
export function buildApiUrl(baseUrl: string, params?: Record<string, any>): string {
  if (!params || Object.keys(params).length === 0) {
    return baseUrl;
  }
  
  const queryString = buildQueryString(params);
  const separator = baseUrl.includes('?') ? '&' : '?';
  
  return `${baseUrl}${separator}${queryString}`;
}

/**
 * Parse API response and handle common response patterns
 * @param response - Fetch response
 * @returns Parsed response data
 */
export async function parseApiResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw {
      status: response.status,
      statusText: response.statusText,
      data: errorData,
      response
    };
  }
  
  const contentType = response.headers.get('content-type');
  
  if (contentType && contentType.includes('application/json')) {
    return response.json();
  }
  
  return response.text() as any;
}

/**
 * Create API request headers
 * @param options - Header options
 * @returns Headers object
 */
export function createApiHeaders(options: {
  contentType?: string;
  authorization?: string;
  apiKey?: string;
  userAgent?: string;
  acceptLanguage?: string;
} = {}): HeadersInit {
  const headers: HeadersInit = {
    'Content-Type': options.contentType || 'application/json',
    'Accept': 'application/json',
  };
  
  if (options.authorization) {
    headers['Authorization'] = options.authorization;
  }
  
  if (options.apiKey) {
    headers['X-API-Key'] = options.apiKey;
  }
  
  if (options.userAgent) {
    headers['User-Agent'] = options.userAgent;
  }
  
  if (options.acceptLanguage) {
    headers['Accept-Language'] = options.acceptLanguage;
  }
  
  return headers;
}

/**
 * Handle API request with retry logic
 * @param url - Request URL
 * @param options - Fetch options
 * @param retryOptions - Retry configuration
 * @returns Promise with response
 */
export async function apiRequestWithRetry<T>(
  url: string,
  options: RequestInit = {},
  retryOptions: {
    maxRetries?: number;
    retryDelay?: number;
    retryCondition?: (error: any) => boolean;
  } = {}
): Promise<T> {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    retryCondition = (error) => error.status >= 500 || error.code === 'NETWORK_ERROR'
  } = retryOptions;
  
  let lastError: any;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(url, options);
      return await parseApiResponse<T>(response);
    } catch (error) {
      lastError = error;
      
      // Don't retry on last attempt or if retry condition is not met
      if (attempt === maxRetries || !retryCondition(error)) {
        break;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)));
    }
  }
  
  throw lastError;
}

/**
 * Validate API response structure
 * @param data - Response data
 * @param schema - Expected schema
 * @returns Validation result
 */
export function validateApiResponse(
  data: any,
  schema: {
    required?: string[];
    optional?: string[];
    types?: Record<string, string>;
  }
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!data || typeof data !== 'object') {
    errors.push('Response data must be an object');
    return { isValid: false, errors };
  }
  
  // Check required fields
  if (schema.required) {
    schema.required.forEach(field => {
      if (!(field in data)) {
        errors.push(`Missing required field: ${field}`);
      }
    });
  }
  
  // Check field types
  if (schema.types) {
    Object.entries(schema.types).forEach(([field, expectedType]) => {
      if (field in data) {
        const actualType = typeof data[field];
        if (actualType !== expectedType) {
          errors.push(`Field ${field} should be ${expectedType}, got ${actualType}`);
        }
      }
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Cache API responses in memory
 */
class ApiCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  
  set(key: string, data: any, ttl: number = 300000): void { // 5 minutes default
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }
  
  get(key: string): any | null {
    const cached = this.cache.get(key);
    
    if (!cached) {
      return null;
    }
    
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }
  
  clear(): void {
    this.cache.clear();
  }
  
  delete(key: string): boolean {
    return this.cache.delete(key);
  }
}

export const apiCache = new ApiCache();

/**
 * Create cache key from URL and parameters
 * @param url - Request URL
 * @param params - Request parameters
 * @returns Cache key string
 */
export function createCacheKey(url: string, params?: Record<string, any>): string {
  const baseKey = url;
  
  if (!params || Object.keys(params).length === 0) {
    return baseKey;
  }
  
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&');
    
  return `${baseKey}?${sortedParams}`;
}

/**
 * Debounce API requests to prevent excessive calls
 * @param fn - Function to debounce
 * @param delay - Debounce delay in milliseconds
 * @returns Debounced function
 */
export function debounceApiRequest<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300
): T {
  let timeoutId: NodeJS.Timeout;
  
  return ((...args: any[]) => {
    clearTimeout(timeoutId);
    
    return new Promise((resolve, reject) => {
      timeoutId = setTimeout(async () => {
        try {
          const result = await fn(...args);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      }, delay);
    });
  }) as T;
}
