// News Image Utilities
// Utilities for handling news images, optimization, and error handling

/**
 * Get optimized image URL with CDN support
 * @param imagePath - The image path from API
 * @param options - Optimization options
 * @returns Optimized image URL
 */
export function getImageUrl(
  imagePath: string,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpg' | 'png';
  } = {}
): string {
  if (!imagePath) {
    return '/images/placeholder-news.jpg';
  }

  // If it's already a full URL, return as is
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  // Build CDN URL
  const cdnDomain = process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || '';
  let imageUrl = `${cdnDomain}${imagePath}`;

  // Add optimization parameters if supported
  const params = new URLSearchParams();
  
  if (options.width) {
    params.append('w', options.width.toString());
  }
  
  if (options.height) {
    params.append('h', options.height.toString());
  }
  
  if (options.quality) {
    params.append('q', options.quality.toString());
  }
  
  if (options.format) {
    params.append('f', options.format);
  }

  // Append parameters if any
  if (params.toString()) {
    imageUrl += `?${params.toString()}`;
  }

  return imageUrl;
}

/**
 * Handle image loading errors
 * @param event - Image error event
 * @param fallbackUrl - Fallback image URL
 */
export function handleImageError(
  event: React.SyntheticEvent<HTMLImageElement>,
  fallbackUrl: string = '/images/placeholder-news.jpg'
): void {
  const target = event.target as HTMLImageElement;
  
  // Prevent infinite loop if fallback also fails
  if (target.src !== fallbackUrl) {
    target.src = fallbackUrl;
  }
}

/**
 * Optimize image URL for different screen sizes
 * @param imagePath - Original image path
 * @param screenSize - Target screen size
 * @returns Optimized image URL
 */
export function optimizeImageUrl(
  imagePath: string,
  screenSize: 'mobile' | 'tablet' | 'desktop' = 'desktop'
): string {
  const sizeMap = {
    mobile: { width: 400, quality: 75 },
    tablet: { width: 800, quality: 80 },
    desktop: { width: 1200, quality: 85 }
  };

  const options = sizeMap[screenSize];
  return getImageUrl(imagePath, options);
}

/**
 * Get responsive image sources for different screen sizes
 * @param imagePath - Original image path
 * @returns Object with responsive image sources
 */
export function getResponsiveImageSources(imagePath: string) {
  return {
    mobile: optimizeImageUrl(imagePath, 'mobile'),
    tablet: optimizeImageUrl(imagePath, 'tablet'),
    desktop: optimizeImageUrl(imagePath, 'desktop')
  };
}

/**
 * Preload critical images
 * @param imageUrls - Array of image URLs to preload
 */
export function preloadImages(imageUrls: string[]): void {
  imageUrls.forEach(url => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = url;
    document.head.appendChild(link);
  });
}

/**
 * Check if image URL is valid
 * @param url - Image URL to check
 * @returns Promise that resolves to boolean
 */
export function isImageValid(url: string): Promise<boolean> {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
  });
}

/**
 * Get image dimensions
 * @param url - Image URL
 * @returns Promise that resolves to image dimensions
 */
export function getImageDimensions(url: string): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = url;
  });
}

/**
 * Generate placeholder image URL
 * @param width - Image width
 * @param height - Image height
 * @param text - Placeholder text
 * @returns Placeholder image URL
 */
export function generatePlaceholder(
  width: number = 400,
  height: number = 300,
  text: string = 'No Image'
): string {
  // Using a simple placeholder service or generate base64 placeholder
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f3f4f6"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" fill="#9ca3af" text-anchor="middle" dy=".3em">
        ${text}
      </text>
    </svg>
  `)}`;
}

/**
 * Image optimization configuration
 */
export const IMAGE_CONFIG = {
  quality: {
    low: 60,
    medium: 75,
    high: 85,
    max: 95
  },
  sizes: {
    thumbnail: { width: 150, height: 150 },
    small: { width: 300, height: 200 },
    medium: { width: 600, height: 400 },
    large: { width: 1200, height: 800 },
    hero: { width: 1920, height: 1080 }
  },
  formats: ['webp', 'jpg', 'png'] as const
};

/**
 * Get optimized image props for Next.js Image component
 * @param imagePath - Image path
 * @param size - Image size preset
 * @returns Props for Next.js Image component
 */
export function getOptimizedImageProps(
  imagePath: string,
  size: keyof typeof IMAGE_CONFIG.sizes = 'medium'
) {
  const sizeConfig = IMAGE_CONFIG.sizes[size];
  const imageUrl = getImageUrl(imagePath, {
    width: sizeConfig.width,
    height: sizeConfig.height,
    quality: IMAGE_CONFIG.quality.high,
    format: 'webp'
  });

  return {
    src: imageUrl,
    width: sizeConfig.width,
    height: sizeConfig.height,
    alt: '',
    onError: (e: React.SyntheticEvent<HTMLImageElement>) => handleImageError(e)
  };
}
