// News Detail Page Component
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { useNewsDetail, useRelatedArticles } from '../hooks';
import { NewsGrid } from '../components';
import { getImageUrl, formatDistanceToNow } from '../utils';
import { vi } from 'date-fns/locale';

interface NewsDetailPageProps {
  slug: string;
  className?: string;
}

/**
 * News Detail Page - Individual article page with related articles
 * @version 1.0.0
 * @since 2024-01-25
 */
export default function NewsDetailPage({
  slug,
  className = ''
}: NewsDetailPageProps) {
  const router = useRouter();
  
  const {
    article,
    loading,
    error,
    shareArticle,
    getReadingProgress,
    markAsRead
  } = useNewsDetail(slug);

  const {
    relatedArticles,
    loading: relatedLoading
  } = useRelatedArticles(slug, 6);

  const [readingProgress, setReadingProgress] = useState(0);
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll for reading progress
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = getReadingProgress(scrollTop, docHeight);
      
      setReadingProgress(progress);
      setIsScrolled(scrollTop > 100);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [getReadingProgress]);

  // Handle article click for related articles
  const handleRelatedArticleClick = useCallback((relatedArticle: any) => {
    router.push(`/news/${relatedArticle.slug}`);
  }, [router]);

  // Handle share
  const handleShare = useCallback(async (platform: 'facebook' | 'twitter' | 'linkedin' | 'copy') => {
    if (shareArticle) {
      await shareArticle(platform);
    }
  }, [shareArticle]);

  // Handle back navigation
  const handleBack = useCallback(() => {
    router.back();
  }, [router]);

  if (loading) {
    return (
      <div className={`min-h-screen bg-white ${className}`}>
        {/* Loading Skeleton */}
        <div className="animate-pulse">
          {/* Header Skeleton */}
          <div className="h-64 md:h-96 bg-gray-200"></div>
          
          <div className="container mx-auto px-4 py-8">
            <div className="max-w-4xl mx-auto">
              <div className="space-y-4">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="space-y-2 mt-8">
                  {Array.from({ length: 8 }).map((_, i) => (
                    <div key={i} className="h-4 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !article) {
    return (
      <div className={`min-h-screen bg-white flex items-center justify-center ${className}`}>
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Không tìm thấy bài viết</h1>
          <p className="text-gray-600 mb-6">{error || 'Bài viết bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.'}</p>
          <div className="space-x-4">
            <button
              onClick={handleBack}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
            >
              Quay lại
            </button>
            <Link
              href="/news"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
            >
              Về trang tin tức
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-white ${className}`}>
      {/* Reading Progress Bar */}
      <div className="fixed top-0 left-0 w-full h-1 bg-gray-200 z-50">
        <div 
          className="h-full bg-blue-600 transition-all duration-150 ease-out"
          style={{ width: `${readingProgress}%` }}
        />
      </div>

      {/* Floating Back Button */}
      {isScrolled && (
        <button
          onClick={handleBack}
          className="fixed top-4 left-4 z-40 p-2 bg-white rounded-full shadow-lg border border-gray-200 hover:bg-gray-50 transition-all duration-200"
        >
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
      )}

      {/* Article Header */}
      <header className="relative">
        {article.featuredImage && (
          <div className="relative h-64 md:h-96 overflow-hidden">
            <Image
              src={getImageUrl(article.featuredImage.url, { width: 1920, height: 1080, quality: 85 })}
              alt={article.featuredImage.alt || article.title}
              fill
              className="object-cover"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
            
            {/* Header Content */}
            <div className="absolute inset-0 flex items-end">
              <div className="container mx-auto px-4 pb-8">
                <div className="max-w-4xl mx-auto text-white">
                  <div className="flex items-center space-x-2 mb-4">
                    <Link
                      href={`/news?category=${article.category.slug}`}
                      className="inline-block px-3 py-1 text-xs font-medium bg-blue-600 rounded-full hover:bg-blue-700 transition-colors duration-200"
                    >
                      {article.category.name}
                    </Link>
                    <span className="text-sm text-gray-300">
                      {formatDistanceToNow(new Date(article.publishedAt), { addSuffix: true, locale: vi })}
                    </span>
                  </div>
                  
                  <h1 className="text-2xl md:text-4xl font-bold leading-tight mb-4">
                    {article.title}
                  </h1>
                  
                  {article.excerpt && (
                    <p className="text-lg text-gray-200 mb-6 line-clamp-3">
                      {article.excerpt}
                    </p>
                  )}
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-gray-300">
                      <span>Bởi {article.author.name}</span>
                      <span>•</span>
                      <span>{article.readingTime} phút đọc</span>
                      <span>•</span>
                      <span>{article.viewCount.toLocaleString()} lượt xem</span>
                    </div>
                    
                    {/* Share Buttons */}
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleShare('facebook')}
                        className="p-2 bg-white/20 rounded-full hover:bg-white/30 transition-colors duration-200"
                        title="Chia sẻ lên Facebook"
                      >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                      </button>
                      
                      <button
                        onClick={() => handleShare('twitter')}
                        className="p-2 bg-white/20 rounded-full hover:bg-white/30 transition-colors duration-200"
                        title="Chia sẻ lên Twitter"
                      >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                        </svg>
                      </button>
                      
                      <button
                        onClick={() => handleShare('copy')}
                        className="p-2 bg-white/20 rounded-full hover:bg-white/30 transition-colors duration-200"
                        title="Sao chép liên kết"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </header>

      {/* Article Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Article Body */}
          <article className="prose prose-lg max-w-none">
            <div 
              dangerouslySetInnerHTML={{ __html: article.content }}
              className="text-gray-800 leading-relaxed"
            />
          </article>

          {/* Article Tags */}
          {article.tags && article.tags.length > 0 && (
            <div className="mt-8 pt-8 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Thẻ:</h3>
              <div className="flex flex-wrap gap-2">
                {article.tags.map((tag) => (
                  <Link
                    key={tag}
                    href={`/news?tags=${tag}`}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors duration-200"
                  >
                    #{tag}
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* Author Info */}
          <div className="mt-8 pt-8 border-t border-gray-200">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                <span className="text-lg font-medium text-gray-600">
                  {article.author.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <h3 className="font-medium text-gray-900">{article.author.name}</h3>
                <p className="text-sm text-gray-600">Tác giả</p>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Related Articles */}
      {relatedArticles.length > 0 && (
        <section className="bg-gray-50 py-12">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-2xl font-bold text-gray-900 mb-8">Bài viết liên quan</h2>
              
              <NewsGrid
                articles={relatedArticles}
                loading={relatedLoading}
                variant="default"
                columns={3}
                gap="md"
                onArticleClick={handleRelatedArticleClick}
                cardVariant="compact"
                showCategory={true}
                showAuthor={false}
                showDate={true}
                showExcerpt={true}
                showReadingTime={false}
                showImage={true}
              />
            </div>
          </div>
        </section>
      )}
    </div>
  );
}
