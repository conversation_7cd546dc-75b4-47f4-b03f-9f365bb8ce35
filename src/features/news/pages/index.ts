// News Pages Exports
// This file manages all page exports for the news feature

import { newsFeatureConfig } from '../config';

// Dynamic imports based on config
const getNewsPage = () => {
  const version = newsFeatureConfig.pages.newsPage;
  return import(`./news-page/v${version}`).then(m => m.default);
};

const getNewsDetailPage = () => {
  const version = newsFeatureConfig.pages.newsDetail;
  return import(`./news-detail-page/v${version}`).then(m => m.default);
};

// Static imports for current versions
export { default as NewsPage } from './news-page';
export { default as NewsDetailPage } from './news-detail-page';

// Export dynamic loaders for advanced usage
export { getNewsPage, getNewsDetailPage };

// Re-export version-specific components if needed
export * from './news-page';
export * from './news-detail-page';
