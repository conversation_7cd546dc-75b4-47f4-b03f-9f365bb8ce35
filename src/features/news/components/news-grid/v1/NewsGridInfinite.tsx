// News Grid Infinite Scroll V1 Component
'use client';

import React, { useEffect, useRef, useCallback } from 'react';
import { NewsGridProps } from '../types';
import { NewsCard } from '../../news-card';
import { Loading } from '../../../../shared/components/ui';

interface NewsGridInfiniteProps extends Omit<NewsGridProps, 'showLoadMore'> {
  onLoadMore: () => void;
  hasMore: boolean;
  loading: boolean;
  threshold?: number; // Distance from bottom to trigger load more
  rootMargin?: string; // Intersection observer root margin
}

/**
 * News Grid Infinite Scroll V1 - Grid with infinite scroll functionality
 * @version 1.0.0
 * @since 2024-01-25
 */
export default function NewsGridInfinite({
  articles,
  loading = false,
  error = null,
  variant = 'default',
  columns = 3,
  gap = 'md',
  hasMore = false,
  onLoadMore,
  onArticleClick,
  className = '',
  cardVariant = 'default',
  showCategory = true,
  showAuthor = true,
  showDate = true,
  showExcerpt = true,
  showReadingTime = true,
  showImage = true,
  threshold = 200,
  rootMargin = '100px'
}: NewsGridInfiniteProps) {
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const isLoadingRef = useRef(false);

  const getGridStyles = () => {
    const gapStyles = {
      sm: 'gap-4',
      md: 'gap-6',
      lg: 'gap-8'
    };

    const columnStyles = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
    };

    switch (variant) {
      case 'masonry':
        return `columns-1 md:columns-2 lg:columns-${columns} ${gapStyles[gap]} space-y-${gap === 'sm' ? '4' : gap === 'md' ? '6' : '8'}`;
      case 'list':
        return `space-y-${gap === 'sm' ? '4' : gap === 'md' ? '6' : '8'}`;
      case 'featured':
        return `grid ${columnStyles[columns]} ${gapStyles[gap]}`;
      default:
        return `grid ${columnStyles[columns]} ${gapStyles[gap]}`;
    }
  };

  const getCardVariantForPosition = (index: number) => {
    if (variant === 'featured' && index === 0) {
      return 'featured';
    }
    return cardVariant;
  };

  const getCardClassName = (index: number) => {
    if (variant === 'masonry') {
      return 'break-inside-avoid mb-4';
    }
    if (variant === 'featured' && index === 0 && columns > 1) {
      return 'md:col-span-2';
    }
    return '';
  };

  // Intersection Observer for infinite scroll
  const handleIntersection = useCallback((entries: IntersectionObserverEntry[]) => {
    const [entry] = entries;
    
    if (entry.isIntersecting && hasMore && !loading && !isLoadingRef.current) {
      isLoadingRef.current = true;
      onLoadMore();
    }
  }, [hasMore, loading, onLoadMore]);

  // Set up intersection observer
  useEffect(() => {
    const observer = new IntersectionObserver(handleIntersection, {
      rootMargin,
      threshold: 0.1
    });

    const currentRef = loadMoreRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [handleIntersection, rootMargin]);

  // Reset loading ref when loading state changes
  useEffect(() => {
    if (!loading) {
      isLoadingRef.current = false;
    }
  }, [loading]);

  // Alternative scroll-based infinite scroll (fallback)
  const handleScroll = useCallback(() => {
    if (hasMore && !loading && !isLoadingRef.current) {
      const scrollTop = window.pageYOffset;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      
      if (scrollTop + windowHeight >= documentHeight - threshold) {
        isLoadingRef.current = true;
        onLoadMore();
      }
    }
  }, [hasMore, loading, onLoadMore, threshold]);

  // Fallback scroll listener (in case Intersection Observer is not supported)
  useEffect(() => {
    // Check if Intersection Observer is supported
    if (!window.IntersectionObserver) {
      window.addEventListener('scroll', handleScroll, { passive: true });
      return () => window.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);

  if (error) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="text-red-500 mb-4">
          <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Có lỗi xảy ra</h3>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  if (loading && articles.length === 0) {
    return (
      <div className={`${className}`}>
        <div className={getGridStyles()}>
          {Array.from({ length: columns * 2 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-gray-200 rounded-lg h-64 mb-4"></div>
              <div className="space-y-2">
                <div className="bg-gray-200 rounded h-4 w-3/4"></div>
                <div className="bg-gray-200 rounded h-4 w-1/2"></div>
                <div className="bg-gray-200 rounded h-3 w-full"></div>
                <div className="bg-gray-200 rounded h-3 w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (articles.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="text-gray-500">
          <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
          </svg>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Không có tin tức</h3>
          <p className="text-gray-600">Hiện tại chưa có bài viết nào để hiển thị.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Articles Grid */}
      <div className={getGridStyles()}>
        {articles.map((article, index) => (
          <div key={article.id} className={getCardClassName(index)}>
            <NewsCard
              article={article}
              variant={getCardVariantForPosition(index)}
              showCategory={showCategory}
              showAuthor={showAuthor}
              showDate={showDate}
              showExcerpt={showExcerpt}
              showReadingTime={showReadingTime}
              showImage={showImage}
              onClick={onArticleClick}
            />
          </div>
        ))}
      </div>

      {/* Infinite Scroll Trigger */}
      {hasMore && (
        <div
          ref={loadMoreRef}
          className="flex justify-center items-center py-8"
          style={{ minHeight: '100px' }}
        >
          {loading ? (
            <div className="flex items-center space-x-3">
              <Loading size="sm" />
              <span className="text-gray-600">Đang tải thêm tin tức...</span>
            </div>
          ) : (
            <div className="text-gray-400 text-sm">
              Cuộn xuống để xem thêm
            </div>
          )}
        </div>
      )}

      {/* End of Content Indicator */}
      {!hasMore && articles.length > 0 && (
        <div className="text-center py-8">
          <div className="inline-flex items-center space-x-2 text-gray-500">
            <div className="h-px bg-gray-300 w-16"></div>
            <span className="text-sm">Đã hiển thị tất cả bài viết</span>
            <div className="h-px bg-gray-300 w-16"></div>
          </div>
        </div>
      )}

      {/* Loading Overlay for Additional Content */}
      {loading && articles.length > 0 && (
        <div className="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 px-4 py-2 z-50">
          <div className="flex items-center space-x-2">
            <Loading size="sm" />
            <span className="text-sm text-gray-600">Đang tải...</span>
          </div>
        </div>
      )}
    </div>
  );
}
