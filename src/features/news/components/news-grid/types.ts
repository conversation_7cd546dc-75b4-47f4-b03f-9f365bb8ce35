// News Grid Component Types
import { NewsArticle } from '../../types';

export interface NewsGridProps {
  articles: NewsArticle[];
  loading?: boolean;
  error?: string | null;
  variant?: 'default' | 'masonry' | 'list' | 'featured';
  columns?: 1 | 2 | 3 | 4;
  gap?: 'sm' | 'md' | 'lg';
  showLoadMore?: boolean;
  hasMore?: boolean;
  onLoadMore?: () => void;
  onArticleClick?: (article: NewsArticle) => void;
  className?: string;
  cardVariant?: 'default' | 'featured' | 'compact' | 'minimal';
  showCategory?: boolean;
  showAuthor?: boolean;
  showDate?: boolean;
  showExcerpt?: boolean;
  showReadingTime?: boolean;
  showImage?: boolean;
}

export interface NewsGridLayoutProps {
  children: React.ReactNode;
  columns: number;
  gap: string;
  variant: string;
  className?: string;
}

export interface LoadMoreButtonProps {
  loading: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
  className?: string;
}
