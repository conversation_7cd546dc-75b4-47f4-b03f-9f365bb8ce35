// News Card Component Version Management
import { newsFeatureConfig } from '../../config';

// Get current version from config
const currentVersion = newsFeatureConfig.components.newsCard;

// Dynamic import based on current version
const NewsCard = () => {
  return import(`./v${currentVersion}`).then(m => m.default);
};

// Static import for current version
export { default } from `./v${currentVersion}`;

// Export version loader
export { NewsCard as getNewsCard };

// Re-export types
export * from './types';
