// News Card Component Types
import { NewsArticle } from '../../types';

export interface NewsCardProps {
  article: NewsArticle;
  variant?: 'default' | 'featured' | 'compact' | 'minimal';
  showCategory?: boolean;
  showAuthor?: boolean;
  showDate?: boolean;
  showExcerpt?: boolean;
  showReadingTime?: boolean;
  showImage?: boolean;
  onClick?: (article: NewsArticle) => void;
  className?: string;
}

export interface NewsCardVariantStyles {
  container: string;
  image: string;
  content: string;
  title: string;
  excerpt: string;
  meta: string;
}

export type NewsCardVariant = 'default' | 'featured' | 'compact' | 'minimal';
