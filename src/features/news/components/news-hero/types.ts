// News Hero Component Types
import { NewsArticle } from '../../types';

export interface NewsHeroProps {
  featuredArticle?: NewsArticle;
  recentArticles?: NewsArticle[];
  loading?: boolean;
  error?: string | null;
  variant?: 'default' | 'split' | 'carousel' | 'minimal';
  showSearch?: boolean;
  searchProps?: {
    value: string;
    onChange: (value: string) => void;
    onSubmit: (value: string) => void;
    placeholder?: string;
  };
  onArticleClick?: (article: NewsArticle) => void;
  className?: string;
  backgroundImage?: string;
  overlay?: boolean;
  height?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface HeroArticleCardProps {
  article: NewsArticle;
  variant: 'featured' | 'secondary';
  onClick?: (article: NewsArticle) => void;
  className?: string;
}

export interface HeroSearchProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (value: string) => void;
  placeholder?: string;
  className?: string;
}
