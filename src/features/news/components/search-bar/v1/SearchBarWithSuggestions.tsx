// Search Bar with Suggestions V1 Component
'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { SearchBarWithSuggestionsProps, SearchSuggestion } from '../types';

/**
 * Search Bar with Suggestions V1 - Enhanced search with autocomplete
 * @version 1.0.0
 * @since 2024-01-25
 */
export default function SearchBarWithSuggestions({
  value,
  onChange,
  onSubmit,
  placeholder = 'T<PERSON><PERSON> kiếm tin tức...',
  disabled = false,
  className = '',
  showButton = true,
  autoFocus = false,
  variant = 'default',
  size = 'md',
  suggestions = [],
  showSuggestions = true,
  onSuggestionClick,
  maxSuggestions = 5
}: SearchBarWithSuggestionsProps) {
  const [localValue, setLocalValue] = useState(value);
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestionsList, setShowSuggestionsList] = useState(false);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const suggestionRefs = useRef<(HTMLButtonElement | null)[]>([]);

  // Filter suggestions based on input
  const filteredSuggestions = suggestions
    .filter(suggestion => 
      suggestion.text.toLowerCase().includes(localValue.toLowerCase()) &&
      suggestion.text.toLowerCase() !== localValue.toLowerCase()
    )
    .slice(0, maxSuggestions);

  const getVariantStyles = () => {
    switch (variant) {
      case 'hero':
        return {
          container: 'relative max-w-2xl mx-auto',
          input: 'w-full pl-12 pr-16 py-4 text-lg bg-white border-2 border-gray-200 rounded-full shadow-lg focus:border-blue-500 focus:shadow-xl transition-all duration-300',
          icon: 'absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400',
          button: 'absolute right-2 top-1/2 transform -translate-y-1/2 px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors duration-200',
          clear: 'absolute right-16 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600',
          suggestions: 'absolute top-full left-0 right-0 mt-2 bg-white border-2 border-gray-200 rounded-2xl shadow-xl z-50 max-h-80 overflow-y-auto'
        };
      case 'compact':
        return {
          container: 'relative',
          input: 'w-full pl-8 pr-10 py-2 text-sm bg-gray-50 border border-gray-200 rounded-md focus:bg-white focus:border-blue-500 focus:outline-none transition-all duration-200',
          icon: 'absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400',
          button: 'absolute right-1 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200',
          clear: 'absolute right-8 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600',
          suggestions: 'absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto'
        };
      default:
        return {
          container: 'relative',
          input: 'w-full pl-10 pr-12 py-3 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-all duration-200',
          icon: 'absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400',
          button: 'absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-gray-400 hover:text-blue-600 transition-colors duration-200',
          clear: 'absolute right-10 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600',
          suggestions: 'absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-50 max-h-72 overflow-y-auto'
        };
    }
  };

  const styles = getVariantStyles();

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue);
    onChange(newValue);
    setSelectedSuggestionIndex(-1);
    
    if (newValue.trim() && showSuggestions) {
      setShowSuggestionsList(true);
    } else {
      setShowSuggestionsList(false);
    }
  }, [onChange, showSuggestions]);

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(localValue);
    setShowSuggestionsList(false);
    inputRef.current?.blur();
  }, [localValue, onSubmit]);

  const handleSuggestionClick = useCallback((suggestion: SearchSuggestion) => {
    setLocalValue(suggestion.text);
    onChange(suggestion.text);
    setShowSuggestionsList(false);
    
    if (onSuggestionClick) {
      onSuggestionClick(suggestion);
    } else {
      onSubmit(suggestion.text);
    }
    
    inputRef.current?.blur();
  }, [onChange, onSubmit, onSuggestionClick]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!showSuggestionsList || filteredSuggestions.length === 0) {
      if (e.key === 'Enter') {
        e.preventDefault();
        onSubmit(localValue);
        setShowSuggestionsList(false);
      }
      if (e.key === 'Escape') {
        inputRef.current?.blur();
        setShowSuggestionsList(false);
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < filteredSuggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : filteredSuggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0) {
          handleSuggestionClick(filteredSuggestions[selectedSuggestionIndex]);
        } else {
          onSubmit(localValue);
          setShowSuggestionsList(false);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setShowSuggestionsList(false);
        setSelectedSuggestionIndex(-1);
        inputRef.current?.blur();
        break;
    }
  }, [localValue, onSubmit, showSuggestionsList, filteredSuggestions, selectedSuggestionIndex, handleSuggestionClick]);

  const handleClear = useCallback(() => {
    setLocalValue('');
    onChange('');
    setShowSuggestionsList(false);
    setSelectedSuggestionIndex(-1);
    inputRef.current?.focus();
  }, [onChange]);

  const handleFocus = useCallback(() => {
    setIsFocused(true);
    if (localValue.trim() && showSuggestions && filteredSuggestions.length > 0) {
      setShowSuggestionsList(true);
    }
  }, [localValue, showSuggestions, filteredSuggestions.length]);

  const handleBlur = useCallback((e: React.FocusEvent) => {
    // Don't hide suggestions if clicking on a suggestion
    if (suggestionsRef.current?.contains(e.relatedTarget as Node)) {
      return;
    }
    
    setIsFocused(false);
    setShowSuggestionsList(false);
    setSelectedSuggestionIndex(-1);
  }, []);

  const getSuggestionIcon = (type: SearchSuggestion['type']) => {
    switch (type) {
      case 'article':
        return (
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
      case 'category':
        return (
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
          </svg>
        );
      case 'tag':
        return (
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        );
    }
  };

  // Update suggestion refs when filtered suggestions change
  useEffect(() => {
    suggestionRefs.current = suggestionRefs.current.slice(0, filteredSuggestions.length);
  }, [filteredSuggestions.length]);

  // Scroll selected suggestion into view
  useEffect(() => {
    if (selectedSuggestionIndex >= 0 && suggestionRefs.current[selectedSuggestionIndex]) {
      suggestionRefs.current[selectedSuggestionIndex]?.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth'
      });
    }
  }, [selectedSuggestionIndex]);

  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  return (
    <form onSubmit={handleSubmit} className={`${styles.container} ${className}`}>
      <div className="relative">
        {/* Search Icon */}
        <div className={styles.icon}>
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>

        {/* Input Field */}
        <input
          ref={inputRef}
          type="text"
          value={localValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          className={`${styles.input} ${
            isFocused ? 'ring-2 ring-blue-200' : ''
          } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
          autoComplete="off"
          role="combobox"
          aria-expanded={showSuggestionsList}
          aria-haspopup="listbox"
          aria-autocomplete="list"
        />

        {/* Clear Button */}
        {localValue && (
          <button
            type="button"
            onClick={handleClear}
            className={styles.clear}
            disabled={disabled}
            tabIndex={-1}
          >
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}

        {/* Search Button */}
        {showButton && (
          <button
            type="submit"
            disabled={disabled || !localValue.trim()}
            className={`${styles.button} ${
              disabled || !localValue.trim() 
                ? 'opacity-50 cursor-not-allowed' 
                : ''
            }`}
          >
            {variant === 'hero' ? (
              'Tìm kiếm'
            ) : (
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            )}
          </button>
        )}

        {/* Suggestions Dropdown */}
        {showSuggestionsList && filteredSuggestions.length > 0 && (
          <div
            ref={suggestionsRef}
            className={styles.suggestions}
            role="listbox"
          >
            {filteredSuggestions.map((suggestion, index) => (
              <button
                key={`${suggestion.type}-${suggestion.id}`}
                ref={el => suggestionRefs.current[index] = el}
                type="button"
                onClick={() => handleSuggestionClick(suggestion)}
                className={`w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center space-x-3 transition-colors duration-150 ${
                  index === selectedSuggestionIndex ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                } ${index === 0 ? 'rounded-t-lg' : ''} ${
                  index === filteredSuggestions.length - 1 ? 'rounded-b-lg' : ''
                }`}
                role="option"
                aria-selected={index === selectedSuggestionIndex}
              >
                {getSuggestionIcon(suggestion.type)}
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium truncate">
                    {suggestion.text}
                  </div>
                  <div className="text-xs text-gray-500 capitalize">
                    {suggestion.type === 'article' ? 'Bài viết' : 
                     suggestion.type === 'category' ? 'Danh mục' : 'Thẻ'}
                  </div>
                </div>
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            ))}
          </div>
        )}
      </div>
    </form>
  );
}
