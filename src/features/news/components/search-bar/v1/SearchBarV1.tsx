// Search Bar V1 Component
'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { SearchBarProps } from '../types';

/**
 * Search Bar V1 - Enhanced search component with variants
 * @version 1.0.0
 * @since 2024-01-25
 */
export default function SearchBarV1({
  value,
  onChange,
  onSubmit,
  placeholder = 'Tìm kiếm tin tức...',
  disabled = false,
  className = '',
  showButton = true,
  autoFocus = false,
  variant = 'default',
  size = 'md'
}: SearchBarProps) {
  const [localValue, setLocalValue] = useState(value);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const getVariantStyles = () => {
    switch (variant) {
      case 'hero':
        return {
          container: 'relative max-w-2xl mx-auto',
          input: 'w-full pl-12 pr-16 py-4 text-lg bg-white border-2 border-gray-200 rounded-full shadow-lg focus:border-blue-500 focus:shadow-xl transition-all duration-300',
          icon: 'absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400',
          button: 'absolute right-2 top-1/2 transform -translate-y-1/2 px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors duration-200',
          clear: 'absolute right-16 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600'
        };
      case 'compact':
        return {
          container: 'relative',
          input: 'w-full pl-8 pr-10 py-2 text-sm bg-gray-50 border border-gray-200 rounded-md focus:bg-white focus:border-blue-500 focus:outline-none transition-all duration-200',
          icon: 'absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400',
          button: 'absolute right-1 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200',
          clear: 'absolute right-8 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600'
        };
      default:
        return {
          container: 'relative',
          input: 'w-full pl-10 pr-12 py-3 bg-white border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-all duration-200',
          icon: 'absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400',
          button: 'absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-gray-400 hover:text-blue-600 transition-colors duration-200',
          clear: 'absolute right-10 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600'
        };
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'text-sm py-2';
      case 'lg':
        return 'text-lg py-4';
      default:
        return 'text-base py-3';
    }
  };

  const styles = getVariantStyles();

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue);
    onChange(newValue);
  }, [onChange]);

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(localValue);
  }, [localValue, onSubmit]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      onSubmit(localValue);
    }
    if (e.key === 'Escape') {
      inputRef.current?.blur();
      setIsFocused(false);
    }
  }, [localValue, onSubmit]);

  const handleClear = useCallback(() => {
    setLocalValue('');
    onChange('');
    inputRef.current?.focus();
  }, [onChange]);

  const handleFocus = useCallback(() => {
    setIsFocused(true);
  }, []);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
  }, []);

  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  return (
    <form onSubmit={handleSubmit} className={`${styles.container} ${className}`}>
      <div className="relative">
        {/* Search Icon */}
        <div className={styles.icon}>
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>

        {/* Input Field */}
        <input
          ref={inputRef}
          type="text"
          value={localValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          className={`${styles.input} ${getSizeStyles()} ${
            isFocused ? 'ring-2 ring-blue-200' : ''
          } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
        />

        {/* Clear Button */}
        {localValue && (
          <button
            type="button"
            onClick={handleClear}
            className={styles.clear}
            disabled={disabled}
          >
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}

        {/* Search Button */}
        {showButton && (
          <button
            type="submit"
            disabled={disabled || !localValue.trim()}
            className={`${styles.button} ${
              disabled || !localValue.trim() 
                ? 'opacity-50 cursor-not-allowed' 
                : ''
            }`}
          >
            {variant === 'hero' ? (
              'Tìm kiếm'
            ) : (
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            )}
          </button>
        )}
      </div>

      {/* Focus Ring Effect for Hero Variant */}
      {variant === 'hero' && isFocused && (
        <div className="absolute inset-0 rounded-full border-2 border-blue-300 animate-pulse pointer-events-none" />
      )}
    </form>
  );
}
