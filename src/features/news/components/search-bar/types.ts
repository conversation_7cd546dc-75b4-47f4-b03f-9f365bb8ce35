// Search Bar Component Types

export interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  showButton?: boolean;
  autoFocus?: boolean;
  variant?: 'default' | 'compact' | 'hero';
  size?: 'sm' | 'md' | 'lg';
}

export interface SearchSuggestion {
  id: string;
  text: string;
  type: 'article' | 'category' | 'tag';
  url?: string;
}

export interface SearchBarWithSuggestionsProps extends SearchBarProps {
  suggestions?: SearchSuggestion[];
  showSuggestions?: boolean;
  onSuggestionClick?: (suggestion: SearchSuggestion) => void;
  maxSuggestions?: number;
}
