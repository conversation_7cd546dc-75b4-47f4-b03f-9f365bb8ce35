// Category Filter Component Types
import { NewsCategory } from '../../types';

export interface CategoryFilterProps {
  categories: NewsCategory[];
  selectedCategory?: string;
  onCategoryChange: (categorySlug: string | undefined) => void;
  showAll?: boolean;
  variant?: 'sidebar' | 'horizontal' | 'dropdown';
  className?: string;
}

export interface CategoryItemProps {
  category: NewsCategory;
  isSelected: boolean;
  onClick: () => void;
  variant?: 'sidebar' | 'horizontal' | 'dropdown';
}
