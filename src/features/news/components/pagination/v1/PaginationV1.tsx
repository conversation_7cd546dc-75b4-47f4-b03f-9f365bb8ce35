// Pagination V1 Component
'use client';

import React from 'react';
import { PaginationProps } from '../types';

/**
 * Pagination V1 - Enhanced pagination component
 * @version 1.0.0
 * @since 2024-01-25
 */
export default function PaginationV1({
  currentPage,
  totalPages,
  onPageChange,
  showFirstLast = true,
  showPrevNext = true,
  maxVisiblePages = 5,
  className = '',
  variant = 'default',
  size = 'md',
  showInfo = true,
  totalItems,
  itemsPerPage
}: PaginationProps) {
  if (totalPages <= 1) return null;

  const getVisiblePages = () => {
    const pages: (number | string)[] = [];
    const halfVisible = Math.floor(maxVisiblePages / 2);
    
    let startPage = Math.max(1, currentPage - halfVisible);
    let endPage = Math.min(totalPages, currentPage + halfVisible);
    
    // Adjust if we're near the beginning or end
    if (endPage - startPage + 1 < maxVisiblePages) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      } else {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
    }
    
    // Add first page and ellipsis if needed
    if (startPage > 1) {
      pages.push(1);
      if (startPage > 2) {
        pages.push('...');
      }
    }
    
    // Add visible pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    // Add ellipsis and last page if needed
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push('...');
      }
      pages.push(totalPages);
    }
    
    return pages;
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return {
          button: 'px-2 py-1 text-xs',
          spacing: 'space-x-1'
        };
      case 'lg':
        return {
          button: 'px-4 py-3 text-base',
          spacing: 'space-x-2'
        };
      default:
        return {
          button: 'px-3 py-2 text-sm',
          spacing: 'space-x-1'
        };
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'simple':
        return {
          button: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50',
          active: 'bg-blue-600 text-white border-blue-600',
          disabled: 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200'
        };
      case 'compact':
        return {
          button: 'bg-gray-100 text-gray-700 hover:bg-gray-200',
          active: 'bg-blue-600 text-white',
          disabled: 'bg-gray-50 text-gray-400 cursor-not-allowed'
        };
      default:
        return {
          button: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:text-gray-900',
          active: 'bg-blue-600 text-white border-blue-600 hover:bg-blue-700',
          disabled: 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200'
        };
    }
  };

  const visiblePages = getVisiblePages();
  const sizeStyles = getSizeStyles();
  const variantStyles = getVariantStyles();

  const handlePageClick = (page: number | string) => {
    if (typeof page === 'number' && page !== currentPage) {
      onPageChange(page);
    }
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const buttonBaseClass = `relative inline-flex items-center font-medium transition-colors duration-200 ${sizeStyles.button}`;

  if (variant === 'simple') {
    return (
      <div className={`flex items-center justify-between ${className}`}>
        <div className="flex-1 flex justify-between sm:hidden">
          <button
            onClick={handlePrevious}
            disabled={currentPage === 1}
            className={`${buttonBaseClass} rounded-l-md ${
              currentPage === 1 ? variantStyles.disabled : variantStyles.button
            }`}
          >
            Trước
          </button>
          <span className="px-4 py-2 text-sm text-gray-700">
            {currentPage} / {totalPages}
          </span>
          <button
            onClick={handleNext}
            disabled={currentPage === totalPages}
            className={`${buttonBaseClass} rounded-r-md ${
              currentPage === totalPages ? variantStyles.disabled : variantStyles.button
            }`}
          >
            Sau
          </button>
        </div>

        <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
          {showInfo && (
            <div>
              <p className="text-sm text-gray-700">
                Trang <span className="font-medium">{currentPage}</span> trong tổng số{' '}
                <span className="font-medium">{totalPages}</span> trang
                {totalItems && itemsPerPage && (
                  <>
                    {' '}({totalItems} kết quả)
                  </>
                )}
              </p>
            </div>
          )}
          
          <div className={`flex ${sizeStyles.spacing}`}>
            {showPrevNext && (
              <button
                onClick={handlePrevious}
                disabled={currentPage === 1}
                className={`${buttonBaseClass} rounded-l-md ${
                  currentPage === 1 ? variantStyles.disabled : variantStyles.button
                }`}
              >
                <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Trước
              </button>
            )}

            {visiblePages.map((page, index) => (
              <button
                key={index}
                onClick={() => handlePageClick(page)}
                disabled={page === '...'}
                className={`${buttonBaseClass} ${
                  page === currentPage
                    ? variantStyles.active
                    : page === '...'
                    ? variantStyles.disabled
                    : variantStyles.button
                } ${!showPrevNext && index === 0 ? 'rounded-l-md' : ''} ${
                  !showPrevNext && index === visiblePages.length - 1 ? 'rounded-r-md' : ''
                }`}
              >
                {page}
              </button>
            ))}

            {showPrevNext && (
              <button
                onClick={handleNext}
                disabled={currentPage === totalPages}
                className={`${buttonBaseClass} rounded-r-md ${
                  currentPage === totalPages ? variantStyles.disabled : variantStyles.button
                }`}
              >
                Sau
                <svg className="h-4 w-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Default and compact variants
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <nav className={`flex ${sizeStyles.spacing}`} aria-label="Pagination">
        {showFirstLast && currentPage > 1 && (
          <button
            onClick={() => handlePageClick(1)}
            className={`${buttonBaseClass} rounded-l-md ${variantStyles.button}`}
          >
            <span className="sr-only">Trang đầu</span>
            <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
          </button>
        )}

        {showPrevNext && (
          <button
            onClick={handlePrevious}
            disabled={currentPage === 1}
            className={`${buttonBaseClass} ${
              !showFirstLast ? 'rounded-l-md' : ''
            } ${currentPage === 1 ? variantStyles.disabled : variantStyles.button}`}
          >
            <span className="sr-only">Trang trước</span>
            <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </button>
        )}

        {visiblePages.map((page, index) => (
          <button
            key={index}
            onClick={() => handlePageClick(page)}
            disabled={page === '...'}
            className={`${buttonBaseClass} ${
              page === currentPage
                ? variantStyles.active
                : page === '...'
                ? variantStyles.disabled
                : variantStyles.button
            }`}
          >
            {page}
          </button>
        ))}

        {showPrevNext && (
          <button
            onClick={handleNext}
            disabled={currentPage === totalPages}
            className={`${buttonBaseClass} ${
              !showFirstLast ? 'rounded-r-md' : ''
            } ${currentPage === totalPages ? variantStyles.disabled : variantStyles.button}`}
          >
            <span className="sr-only">Trang sau</span>
            <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </button>
        )}

        {showFirstLast && currentPage < totalPages && (
          <button
            onClick={() => handlePageClick(totalPages)}
            className={`${buttonBaseClass} rounded-r-md ${variantStyles.button}`}
          >
            <span className="sr-only">Trang cuối</span>
            <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414zm6 0a1 1 0 011.414 0l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414-1.414L14.586 10l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </nav>
    </div>
  );
}
