// Pagination Component Types

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
  showPrevNext?: boolean;
  maxVisiblePages?: number;
  className?: string;
  variant?: 'default' | 'simple' | 'compact';
  size?: 'sm' | 'md' | 'lg';
  showInfo?: boolean;
  totalItems?: number;
  itemsPerPage?: number;
}

export interface PaginationButtonProps {
  page: number | string;
  isActive?: boolean;
  isDisabled?: boolean;
  onClick: (page: number) => void;
  children: React.ReactNode;
  className?: string;
}

export interface PaginationInfoProps {
  currentPage: number;
  totalPages: number;
  totalItems?: number;
  itemsPerPage?: number;
  className?: string;
}
