// useNews Hook
// Custom hook for managing news articles data

'use client';

import { useState, useEffect, useCallback } from 'react';
import { NewsService } from '../services/NewsService';
import { NewsArticle, NewsFilter, NewsSortOption, UseNewsReturn } from '../types';

/**
 * Custom hook for managing news articles
 * @param initialFilters - Initial filter values
 * @param initialSort - Initial sort option
 * @param initialPage - Initial page number
 * @returns News data and management functions
 */
export function useNews(
  initialFilters: NewsFilter = {},
  initialSort: NewsSortOption = 'publishedAt_desc',
  initialPage: number = 1
): UseNewsReturn {
  const [articles, setArticles] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [filters, setFilters] = useState<NewsFilter>(initialFilters);
  const [sort, setSort] = useState<NewsSortOption>(initialSort);

  /**
   * Fetch articles with current filters and sort
   */
  const fetchArticles = useCallback(async (page: number = 1, append: boolean = false) => {
    try {
      setLoading(true);
      setError(null);

      const response = await NewsService.getArticles(filters, sort, page);
      
      if (append) {
        setArticles(prev => [...prev, ...response.data]);
      } else {
        setArticles(response.data);
      }

      setHasMore(response.meta.hasNextPage);
      setCurrentPage(page);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải tin tức');
      console.error('Error fetching articles:', err);
    } finally {
      setLoading(false);
    }
  }, [filters, sort]);

  /**
   * Load more articles (pagination)
   */
  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      fetchArticles(currentPage + 1, true);
    }
  }, [loading, hasMore, currentPage, fetchArticles]);

  /**
   * Refresh articles (reload from first page)
   */
  const refresh = useCallback(() => {
    setCurrentPage(1);
    fetchArticles(1, false);
  }, [fetchArticles]);

  /**
   * Update filters and refetch
   */
  const updateFilters = useCallback((newFilters: NewsFilter) => {
    setFilters(newFilters);
    setCurrentPage(1);
  }, []);

  /**
   * Update sort and refetch
   */
  const updateSort = useCallback((newSort: NewsSortOption) => {
    setSort(newSort);
    setCurrentPage(1);
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchArticles(1, false);
  }, [fetchArticles]);

  // Refetch when filters or sort change
  useEffect(() => {
    if (currentPage === 1) {
      fetchArticles(1, false);
    }
  }, [filters, sort, currentPage, fetchArticles]);

  return {
    articles,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    updateFilters,
    updateSort,
    currentPage,
    filters,
    sort
  };
}
