// useNewsFilters Hook
// Custom hook for managing news filters

'use client';

import { useState, useCallback, useMemo } from 'react';
import { NewsFilter, NewsSortOption, UseNewsFiltersReturn } from '../types';

/**
 * Custom hook for managing news filters
 * @param initialFilters - Initial filter values
 * @returns Filter state and management functions
 */
export function useNewsFilters(
  initialFilters: NewsFilter = {}
): UseNewsFiltersReturn {
  const [filters, setFilters] = useState<NewsFilter>(initialFilters);
  const [sort, setSort] = useState<NewsSortOption>('publishedAt_desc');

  /**
   * Update a specific filter
   */
  const updateFilter = useCallback((key: keyof NewsFilter, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  /**
   * Clear all filters
   */
  const clearFilters = useCallback(() => {
    setFilters({});
    setSort('publishedAt_desc');
  }, []);

  /**
   * Apply filters (for external use)
   */
  const applyFilters = useCallback(() => {
    // This can be used to trigger external actions when filters are applied
    // For example, analytics tracking or URL updates
    console.log('Filters applied:', filters);
  }, [filters]);

  /**
   * Update sort option
   */
  const updateSort = useCallback((newSort: NewsSortOption) => {
    setSort(newSort);
  }, []);

  /**
   * Check if any filters are active
   */
  const hasActiveFilters = useMemo(() => {
    return Object.values(filters).some(value => {
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      return value !== undefined && value !== null && value !== '';
    });
  }, [filters]);

  /**
   * Get filter count
   */
  const filterCount = useMemo(() => {
    let count = 0;
    Object.values(filters).forEach(value => {
      if (Array.isArray(value)) {
        count += value.length;
      } else if (value !== undefined && value !== null && value !== '') {
        count += 1;
      }
    });
    return count;
  }, [filters]);

  /**
   * Reset specific filter
   */
  const resetFilter = useCallback((key: keyof NewsFilter) => {
    setFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[key];
      return newFilters;
    });
  }, []);

  /**
   * Set multiple filters at once
   */
  const setMultipleFilters = useCallback((newFilters: Partial<NewsFilter>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }));
  }, []);

  /**
   * Toggle a tag in the tags filter
   */
  const toggleTag = useCallback((tag: string) => {
    setFilters(prev => {
      const currentTags = prev.tags || [];
      const newTags = currentTags.includes(tag)
        ? currentTags.filter(t => t !== tag)
        : [...currentTags, tag];
      
      return {
        ...prev,
        tags: newTags.length > 0 ? newTags : undefined
      };
    });
  }, []);

  /**
   * Set date range filter
   */
  const setDateRange = useCallback((dateFrom?: string, dateTo?: string) => {
    setFilters(prev => ({
      ...prev,
      dateFrom,
      dateTo
    }));
  }, []);

  /**
   * Get query parameters for API calls
   */
  const getQueryParams = useCallback(() => {
    const params: Record<string, any> = {};
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          params[key] = value.join(',');
        } else if (!Array.isArray(value)) {
          params[key] = value;
        }
      }
    });
    
    params.sort = sort;
    
    return params;
  }, [filters, sort]);

  return {
    filters,
    sort,
    updateFilter,
    updateSort,
    clearFilters,
    applyFilters,
    resetFilter,
    setMultipleFilters,
    toggleTag,
    setDateRange,
    hasActiveFilters,
    filterCount,
    getQueryParams
  };
}
