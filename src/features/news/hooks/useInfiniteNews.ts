// useInfiniteNews Hook
// Custom hook for infinite scroll news loading

'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { NewsService } from '../services/NewsService';
import { NewsArticle, NewsFilter, NewsSortOption } from '../types';
import { parseNewsError } from '../utils';

interface UseInfiniteNewsOptions {
  initialFilters?: NewsFilter;
  initialSort?: NewsSortOption;
  pageSize?: number;
  autoFetch?: boolean;
  maxPages?: number;
}

interface UseInfiniteNewsReturn {
  articles: NewsArticle[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  currentPage: number;
  totalPages: number;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  reset: () => void;
  updateFilters: (filters: NewsFilter) => void;
  updateSort: (sort: NewsSortOption) => void;
  filters: NewsFilter;
  sort: NewsSortOption;
  isInitialLoading: boolean;
  isLoadingMore: boolean;
}

/**
 * Custom hook for infinite scroll news loading
 * @param options - Configuration options
 * @returns Infinite news data and management functions
 */
export function useInfiniteNews({
  initialFilters = {},
  initialSort = 'publishedAt_desc',
  pageSize = 20,
  autoFetch = true,
  maxPages = 50
}: UseInfiniteNewsOptions = {}): UseInfiniteNewsReturn {
  const [articles, setArticles] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [filters, setFilters] = useState<NewsFilter>(initialFilters);
  const [sort, setSort] = useState<NewsSortOption>(initialSort);

  const isInitialLoading = loading && articles.length === 0;
  const abortControllerRef = useRef<AbortController | null>(null);
  const lastRequestRef = useRef<string>('');

  /**
   * Generate request key for deduplication
   */
  const generateRequestKey = useCallback((requestFilters: NewsFilter, requestSort: NewsSortOption, page: number) => {
    return JSON.stringify({ filters: requestFilters, sort: requestSort, page });
  }, []);

  /**
   * Fetch articles for a specific page
   */
  const fetchArticles = useCallback(async (
    requestFilters: NewsFilter,
    requestSort: NewsSortOption,
    page: number,
    append: boolean = false
  ) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    // Generate request key for deduplication
    const requestKey = generateRequestKey(requestFilters, requestSort, page);
    
    // Skip if same request is already in progress
    if (lastRequestRef.current === requestKey) {
      return;
    }
    
    lastRequestRef.current = requestKey;

    try {
      if (append) {
        setIsLoadingMore(true);
      } else {
        setLoading(true);
      }
      setError(null);

      console.log('🔄 Fetching articles:', { filters: requestFilters, sort: requestSort, page });

      const response = await NewsService.getInstance().getArticles(
        requestFilters,
        requestSort,
        page
      );

      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      const newArticles = response.data || [];
      const pagination = response.pagination;

      if (append) {
        setArticles(prev => {
          // Deduplicate articles by ID
          const existingIds = new Set(prev.map(article => article.id));
          const uniqueNewArticles = newArticles.filter(article => !existingIds.has(article.id));
          return [...prev, ...uniqueNewArticles];
        });
      } else {
        setArticles(newArticles);
      }

      // Update pagination info
      if (pagination) {
        setCurrentPage(pagination.page);
        setTotalPages(pagination.totalPages);
        setHasMore(pagination.page < pagination.totalPages && pagination.page < maxPages);
      } else {
        // Fallback pagination logic
        const hasMoreArticles = newArticles.length === pageSize;
        setHasMore(hasMoreArticles && page < maxPages);
        setCurrentPage(page);
      }

      console.log('✅ Articles fetched successfully:', {
        newCount: newArticles.length,
        totalCount: append ? articles.length + newArticles.length : newArticles.length,
        hasMore: hasMore,
        page
      });

    } catch (err) {
      // Don't set error if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      const parsedError = parseNewsError(err);
      setError(parsedError.message);
      console.error('❌ Error fetching articles:', parsedError);

      // If this was a load more request, don't clear existing articles
      if (!append) {
        setArticles([]);
      }
    } finally {
      if (!abortControllerRef.current?.signal.aborted) {
        setLoading(false);
        setIsLoadingMore(false);
        lastRequestRef.current = '';
      }
    }
  }, [articles.length, hasMore, maxPages, pageSize, generateRequestKey]);

  /**
   * Load more articles (next page)
   */
  const loadMore = useCallback(async () => {
    if (!hasMore || loading || isLoadingMore) {
      return;
    }

    const nextPage = currentPage + 1;
    await fetchArticles(filters, sort, nextPage, true);
  }, [hasMore, loading, isLoadingMore, currentPage, filters, sort, fetchArticles]);

  /**
   * Refresh articles (reload from first page)
   */
  const refresh = useCallback(async () => {
    setCurrentPage(1);
    setHasMore(true);
    await fetchArticles(filters, sort, 1, false);
  }, [filters, sort, fetchArticles]);

  /**
   * Reset to initial state
   */
  const reset = useCallback(() => {
    // Cancel any ongoing requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    setArticles([]);
    setLoading(false);
    setIsLoadingMore(false);
    setError(null);
    setCurrentPage(1);
    setTotalPages(0);
    setHasMore(true);
    setFilters(initialFilters);
    setSort(initialSort);
    lastRequestRef.current = '';
  }, [initialFilters, initialSort]);

  /**
   * Update filters and refresh
   */
  const updateFilters = useCallback((newFilters: NewsFilter) => {
    setFilters(newFilters);
    setCurrentPage(1);
    setHasMore(true);
    setArticles([]); // Clear existing articles immediately for better UX
  }, []);

  /**
   * Update sort and refresh
   */
  const updateSort = useCallback((newSort: NewsSortOption) => {
    setSort(newSort);
    setCurrentPage(1);
    setHasMore(true);
    setArticles([]); // Clear existing articles immediately for better UX
  }, []);

  /**
   * Debounced fetch effect
   */
  useEffect(() => {
    if (!autoFetch) return;

    const timeoutId = setTimeout(() => {
      fetchArticles(filters, sort, 1, false);
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [filters, sort, autoFetch, fetchArticles]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  /**
   * Auto-fetch on mount
   */
  useEffect(() => {
    if (autoFetch && articles.length === 0 && !loading) {
      fetchArticles(filters, sort, 1, false);
    }
  }, [autoFetch]); // Only run on mount

  return {
    articles,
    loading,
    error,
    hasMore,
    currentPage,
    totalPages,
    loadMore,
    refresh,
    reset,
    updateFilters,
    updateSort,
    filters,
    sort,
    isInitialLoading,
    isLoadingMore
  };
}
