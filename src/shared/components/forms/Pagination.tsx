// Shared Pagination Component
'use client';

import React from 'react';

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
  showPrevNext?: boolean;
  maxVisiblePages?: number;
  className?: string;
}

/**
 * Reusable Pagination component
 * Used across features for consistent pagination
 */
export default function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  showFirstLast = true,
  showPrevNext = true,
  maxVisiblePages = 5,
  className = ''
}: PaginationProps) {
  if (totalPages <= 1) return null;

  const getVisiblePages = () => {
    const pages: (number | string)[] = [];
    const halfVisible = Math.floor(maxVisiblePages / 2);
    
    let startPage = Math.max(1, currentPage - halfVisible);
    let endPage = Math.min(totalPages, currentPage + halfVisible);
    
    // Adjust if we're near the beginning or end
    if (endPage - startPage + 1 < maxVisiblePages) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      } else {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
    }
    
    // Add first page and ellipsis if needed
    if (startPage > 1) {
      pages.push(1);
      if (startPage > 2) {
        pages.push('...');
      }
    }
    
    // Add visible pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    // Add ellipsis and last page if needed
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push('...');
      }
      pages.push(totalPages);
    }
    
    return pages;
  };

  const visiblePages = getVisiblePages();

  const handlePageClick = (page: number | string) => {
    if (typeof page === 'number' && page !== currentPage) {
      onPageChange(page);
    }
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const buttonBaseClass = "relative inline-flex items-center px-4 py-2 text-sm font-medium border transition-colors duration-200";
  const activeClass = "z-10 bg-blue-600 border-blue-600 text-white";
  const inactiveClass = "bg-white border-gray-300 text-gray-500 hover:bg-gray-50 hover:text-gray-700";
  const disabledClass = "bg-gray-100 border-gray-300 text-gray-400 cursor-not-allowed";

  return (
    <nav className={`flex items-center justify-between ${className}`} aria-label="Pagination">
      <div className="flex-1 flex justify-between sm:hidden">
        {/* Mobile pagination */}
        <button
          onClick={handlePrevious}
          disabled={currentPage === 1}
          className={`${buttonBaseClass} rounded-l-md ${
            currentPage === 1 ? disabledClass : inactiveClass
          }`}
        >
          Trước
        </button>
        <span className="px-4 py-2 text-sm text-gray-700">
          Trang {currentPage} / {totalPages}
        </span>
        <button
          onClick={handleNext}
          disabled={currentPage === totalPages}
          className={`${buttonBaseClass} rounded-r-md ${
            currentPage === totalPages ? disabledClass : inactiveClass
          }`}
        >
          Sau
        </button>
      </div>

      <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p className="text-sm text-gray-700">
            Hiển thị trang <span className="font-medium">{currentPage}</span> trong tổng số{' '}
            <span className="font-medium">{totalPages}</span> trang
          </p>
        </div>
        
        <div>
          <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            {/* First page button */}
            {showFirstLast && currentPage > 1 && (
              <button
                onClick={() => handlePageClick(1)}
                className={`${buttonBaseClass} rounded-l-md ${inactiveClass}`}
              >
                <span className="sr-only">Trang đầu</span>
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
              </button>
            )}

            {/* Previous button */}
            {showPrevNext && (
              <button
                onClick={handlePrevious}
                disabled={currentPage === 1}
                className={`${buttonBaseClass} ${
                  !showFirstLast ? 'rounded-l-md' : ''
                } ${currentPage === 1 ? disabledClass : inactiveClass}`}
              >
                <span className="sr-only">Trang trước</span>
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </button>
            )}

            {/* Page numbers */}
            {visiblePages.map((page, index) => (
              <button
                key={index}
                onClick={() => handlePageClick(page)}
                disabled={page === '...'}
                className={`${buttonBaseClass} ${
                  page === currentPage
                    ? activeClass
                    : page === '...'
                    ? disabledClass
                    : inactiveClass
                }`}
              >
                {page}
              </button>
            ))}

            {/* Next button */}
            {showPrevNext && (
              <button
                onClick={handleNext}
                disabled={currentPage === totalPages}
                className={`${buttonBaseClass} ${
                  !showFirstLast ? 'rounded-r-md' : ''
                } ${currentPage === totalPages ? disabledClass : inactiveClass}`}
              >
                <span className="sr-only">Trang sau</span>
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </button>
            )}

            {/* Last page button */}
            {showFirstLast && currentPage < totalPages && (
              <button
                onClick={() => handlePageClick(totalPages)}
                className={`${buttonBaseClass} rounded-r-md ${inactiveClass}`}
              >
                <span className="sr-only">Trang cuối</span>
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414zm6 0a1 1 0 011.414 0l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414-1.414L14.586 10l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            )}
          </nav>
        </div>
      </div>
    </nav>
  );
}
