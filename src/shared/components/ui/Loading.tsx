// Shared Loading Component
'use client';

import React from 'react';

export interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'spinner' | 'skeleton' | 'dots';
  text?: string;
  className?: string;
}

/**
 * Reusable Loading component
 * Used across all features for consistent loading states
 */
export default function Loading({
  size = 'md',
  variant = 'spinner',
  text,
  className = ''
}: LoadingProps) {
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'lg':
        return 'w-8 h-8';
      default: // md
        return 'w-6 h-6';
    }
  };

  const sizeStyles = getSizeStyles();

  if (variant === 'skeleton') {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="bg-gray-200 rounded h-4 w-full mb-2"></div>
        <div className="bg-gray-200 rounded h-4 w-3/4 mb-2"></div>
        <div className="bg-gray-200 rounded h-4 w-1/2"></div>
      </div>
    );
  }

  if (variant === 'dots') {
    return (
      <div className={`flex items-center justify-center space-x-1 ${className}`}>
        <div className={`${sizeStyles} bg-blue-600 rounded-full animate-bounce`}></div>
        <div className={`${sizeStyles} bg-blue-600 rounded-full animate-bounce`} style={{ animationDelay: '0.1s' }}></div>
        <div className={`${sizeStyles} bg-blue-600 rounded-full animate-bounce`} style={{ animationDelay: '0.2s' }}></div>
      </div>
    );
  }

  // Default spinner variant
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <svg
        className={`animate-spin ${sizeStyles} text-blue-600`}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
      {text && <span className="ml-2 text-gray-600">{text}</span>}
    </div>
  );
}
